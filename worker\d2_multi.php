<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

// 从命令行参数获取配置，或使用默认值
$storeParam = $argv[1] ?? '37';  // 可以是单个ID或逗号分隔的多个ID，或者'all'表示所有店铺
$status = $argv[2] ?? 'delivered';  // awaiting_packaging, awaiting_deliver, delivering, delivered, all
$days = $argv[3] ?? 90;  // 查询天数
$debug = isset($argv[4]) && $argv[4] === 'debug'; // 是否开启调试模式

echo "=== Ozon 多店铺订单同步脚本 ===\n";
echo "店铺参数: {$storeParam}\n";
echo "订单状态: {$status}\n";
echo "查询天数: {$days}\n";
echo "调试模式: " . ($debug ? '开启' : '关闭') . "\n\n";

// 解析店铺ID参数
$storeIds = [];
if ($storeParam === 'all') {
    // 获取所有激活的店铺
    $stores = $DB->findAll('store', ['id', 'ClientId', 'status'], ['status' => 1]);
    if (empty($stores)) {
        $stores = $DB->findAll('store', ['id', 'ClientId', 'status'], []);
    }
    foreach ($stores as $store) {
        $storeIds[] = $store['id'];
    }
    echo "获取到所有店铺ID: " . implode(', ', $storeIds) . "\n\n";
} else {
    // 解析逗号分隔的店铺ID
    $storeIds = array_map('trim', explode(',', $storeParam));
    $storeIds = array_filter($storeIds, 'is_numeric');
    echo "解析的店铺ID: " . implode(', ', $storeIds) . "\n\n";
}

if (empty($storeIds)) {
    echo "错误: 没有有效的店铺ID\n";
    
    // 显示可用店铺
    echo "\n可用店铺列表:\n";
    $stores = $DB->findAll('store', ['id', 'ClientId', 'status'], []);
    foreach ($stores as $store) {
        echo "- ID: {$store['id']}, ClientId: {$store['ClientId']}, 状态: " . ($store['status'] ?? '未知') . "\n";
    }
    exit(1);
}

// 定义单店铺同步函数
function syncSingleStore($DB, $storeId, $status, $days, $debug) {
    echo "\n=== 同步店铺ID: {$storeId} ===\n";
    
    // 获取店铺数据
    $row = $DB->find('store', '*', ['id' => $storeId]);
    if (!$row) {
        echo "错误: 找不到ID为{$storeId}的店铺\n";
        return [
            'code' => -1,
            'message' => "店铺ID {$storeId} 不存在",
            'store_id' => $storeId,
            'order_count' => 0
        ];
    }

    $offset = 0;
    $orderNumbers = [];
    $totalOrders = 0;
    
    // 计算时间范围
    $since = new DateTime("-{$days} days", new DateTimeZone('UTC'));
    $to = new DateTime('yesterday', new DateTimeZone('UTC'));
    $to->setTime(23, 59, 59);
    
    echo "店铺ClientId: {$row['ClientId']}\n";
    echo "时间范围: " . $since->format('Y-m-d H:i:s') . " 到 " . $to->format('Y-m-d H:i:s') . "\n";
    
    // 构建请求参数
    $requestParams = [
        "dir" => "ASC",
        "filter" => [
            "since" => $since->format('Y-m-d\TH:i:s.v\Z'),
            "status" => $status,
            "to" => $to->format('Y-m-d\TH:i:s.v\Z')
        ],
        "limit" => 100,
        "offset" => 0
    ];
    
    if ($debug) {
        echo "请求参数: " . json_encode($requestParams, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    $pageCount = 0;
    while (true) {
        $pageCount++;
        $requestParams['offset'] = $offset;
        
        echo "正在获取第 {$pageCount} 页数据...";
        
        try {
            $url = 'https://api-seller.ozon.ru/v3/posting/fbs/list';
            
            $ch = curl_init();
            $headers = [
                'Client-Id: ' . $row['ClientId'],
                'Api-Key: ' . $row['key'],
                'Content-Type: application/json'
            ];
            
            $jsonData = json_encode($requestParams, JSON_UNESCAPED_UNICODE);
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POSTFIELDS => $jsonData,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            if ($curlError) {
                echo " cURL错误: {$curlError}\n";
                break;
            }
            
            if ($httpCode !== 200) {
                echo " HTTP错误: {$httpCode}\n";
                if ($debug) {
                    echo "API响应: {$response}\n";
                }
                break;
            }
            
            $data = json_decode($response, true);
            
            if ($debug) {
                echo "\nAPI响应: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
            }
            
            // 检查是否有错误信息
            if (isset($data['error'])) {
                echo " API返回错误: " . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . "\n";
                break;
            }
            
            if (isset($data['result']['postings']) && !empty($data['result']['postings'])) {
                $currentBatchCount = count($data['result']['postings']);
                echo " 获取到 {$currentBatchCount} 个订单\n";
                
                foreach ($data['result']['postings'] as $item) {
                    $orderNumbers[] = [
                        'posting_number' => $item['posting_number'],
                        'status' => $item['status'] ?? '未知',
                        'created_at' => $item['created_at'] ?? '',
                        'in_process_at' => $item['in_process_at'] ?? '',
                        'store_id' => $storeId
                    ];
                    $totalOrders++;
                }
                
                $offset += $currentBatchCount;
                
                // 如果返回的数据量小于limit，说明没有更多数据了
                if ($currentBatchCount < 100) {
                    echo "店铺 {$storeId} 已获取所有数据\n";
                    break;
                }
                
                // 添加延迟避免API限流
                usleep(500000); // 0.5秒延迟
            } else {
                echo " 没有更多订单数据\n";
                break;
            }
        } catch (Exception $e) {
            echo " API调用失败: " . $e->getMessage() . "\n";
            break;
        }
        
        // 防止无限循环，设置最大偏移量
        if ($offset >= 10000) {
            echo "店铺 {$storeId} 已达到最大偏移量限制\n";
            break;
        }
    }
    
    // 保存订单号到JSON文件
    $filename = "{$storeId}_{$status}_{$days}days.json";
    $filePath = __DIR__ . '/data/' . $filename;
    
    // 确保目录存在
    if (!is_dir(dirname($filePath))) {
        mkdir(dirname($filePath), 0755, true);
    }
    
    // 写入JSON文件
    file_put_contents($filePath, json_encode($orderNumbers, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    echo "店铺 {$storeId} 订单同步完成!\n";
    echo "店铺 {$storeId} 获取订单数量: {$totalOrders}\n";
    echo "数据已保存到: {$filePath}\n";
    
    // 显示状态统计
    $statusCount = [];
    if (!empty($orderNumbers)) {
        foreach ($orderNumbers as $order) {
            $statusCount[$order['status']] = ($statusCount[$order['status']] ?? 0) + 1;
        }
        
        echo "店铺 {$storeId} 订单状态统计:\n";
        foreach ($statusCount as $orderStatus => $count) {
            echo "  - {$orderStatus}: {$count} 个\n";
        }
    }
    
    return [
        'code' => 0,
        'message' => '订单数据已保存',
        'store_id' => $storeId,
        'filename' => $filename,
        'order_count' => $totalOrders,
        'file_path' => $filePath,
        'status_filter' => $status,
        'days' => $days,
        'status_count' => $statusCount,
        'time_range' => [
            'since' => $since->format('Y-m-d H:i:s'),
            'to' => $to->format('Y-m-d H:i:s')
        ]
    ];
}

// 总计统计
$totalStores = count($storeIds);
$globalOrderCount = 0;
$globalResults = [];

echo "开始同步 {$totalStores} 个店铺...\n";

// 遍历所有店铺进行同步
foreach ($storeIds as $index => $storeId) {
    echo "\n[" . ($index + 1) . "/{$totalStores}] ";
    $result = syncSingleStore($DB, $storeId, $status, $days, $debug);
    $globalResults[] = $result;
    $globalOrderCount += $result['order_count'];
    
    // 在店铺之间添加延迟，避免API限流
    if ($index < count($storeIds) - 1) {
        echo "等待 1 秒后继续下一个店铺...\n";
        sleep(1);
    }
}

// 总结报告
echo "\n" . str_repeat('=', 60) . "\n";
echo "所有店铺同步完成!\n";
echo "同步店铺数量: {$totalStores}\n";
echo "总订单数量: {$globalOrderCount}\n";
echo "订单状态: {$status}\n";
echo "查询天数: {$days}\n\n";

// 显示各店铺统计
echo "各店铺详情:\n";
foreach ($globalResults as $result) {
    if ($result['code'] === 0) {
        echo "- 店铺 {$result['store_id']}: {$result['order_count']} 个订单";
        if (!empty($result['status_count'])) {
            $statusList = [];
            foreach ($result['status_count'] as $status => $count) {
                $statusList[] = "{$status}({$count})";
            }
            echo " [" . implode(', ', $statusList) . "]";
        }
        echo "\n";
    } else {
        echo "- 店铺 {$result['store_id']}: 失败 - {$result['message']}\n";
    }
}

// 合并所有店铺数据（可选）
if ($totalStores > 1) {
    $allOrdersFilename = "all_stores_{$status}_{$days}days.json";
    $allOrdersPath = __DIR__ . '/data/' . $allOrdersFilename;
    
    $allOrders = [];
    foreach ($globalResults as $result) {
        if ($result['code'] === 0 && file_exists($result['file_path'])) {
            $storeOrders = json_decode(file_get_contents($result['file_path']), true);
            if ($storeOrders) {
                $allOrders = array_merge($allOrders, $storeOrders);
            }
        }
    }
    
    if (!empty($allOrders)) {
        file_put_contents($allOrdersPath, json_encode($allOrders, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "\n合并数据已保存到: {$allOrdersPath}\n";
    }
}

// 输出最终结果
$finalResult = [
    'code' => 0,
    'message' => '多店铺订单同步完成',
    'total_stores' => $totalStores,
    'total_orders' => $globalOrderCount,
    'status_filter' => $status,
    'days' => $days,
    'stores' => $globalResults
];

echo "\n" . json_encode($finalResult, JSON_UNESCAPED_UNICODE) . "\n";