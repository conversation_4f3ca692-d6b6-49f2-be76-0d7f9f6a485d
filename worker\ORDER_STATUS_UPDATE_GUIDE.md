# 订单状态更新工具使用说明

## 概述

创建了两个脚本来将同步得到的JSON数据中的订单状态更新到`ozon_order`表中：

1. `update_order_status.php` - 功能完整的订单状态更新工具
2. `quick_update_status.php` - 简化版一键更新工具

## 🚀 快速使用

### 最简单的方式（推荐）

```bash
# 一键更新data目录下所有JSON文件中的订单状态
php quick_update_status.php
```

这个脚本会：
- 自动扫描`worker/data`目录下的所有JSON文件
- 排除合并文件（`all_stores_`开头的文件）
- 批量更新所有订单状态到数据库
- 显示详细的处理进度和统计信息

## 📋 完整功能版本

### 基本使用方法

```bash
# 显示帮助信息
php update_order_status.php help

# 查看data目录下的所有JSON文件
php update_order_status.php list
```

### 更新模式

#### 1. 更新单个JSON文件
```bash
# 更新指定的JSON文件
php update_order_status.php file 37_delivered_90days.json

# 开启调试模式
php update_order_status.php file 37_delivered_90days.json debug
```

#### 2. 更新指定店铺的所有文件
```bash
# 更新店铺37的所有JSON文件
php update_order_status.php store 37

# 开启调试模式
php update_order_status.php store 37 debug
```

#### 3. 更新所有JSON文件
```bash
# 更新data目录下的所有JSON文件
php update_order_status.php all

# 开启调试模式
php update_order_status.php all debug
```

#### 4. 更新指定状态的文件
```bash
# 更新所有delivered状态的文件
php update_order_status.php status delivered

# 更新所有awaiting_packaging状态的文件
php update_order_status.php status awaiting_packaging
```

## 📊 输出信息说明

### 处理统计
- **处理订单数**: 从JSON文件中读取的订单总数
- **更新成功**: 成功更新到数据库的订单数
- **无需更新**: 状态已经是最新的订单数
- **订单不存在**: 在数据库中找不到的订单数
- **处理错误**: 处理过程中出现错误的订单数

### 状态变更统计
显示具体的状态变更情况，例如：
```
delivering → delivered: 150 个
awaiting_deliver → delivering: 50 个
awaiting_packaging → awaiting_deliver: 30 个
```

## 🔄 典型工作流程

### 完整的同步和更新流程

```bash
# 1. 同步订单数据到JSON文件
php sync_custom_stores.php delivered 30

# 2. 更新订单状态到数据库
php quick_update_status.php
```

### 定期维护流程

```bash
# 每日更新流程
php batch_sync.php preset daily
php quick_update_status.php

# 每周全量更新
php batch_sync.php preset weekly  
php quick_update_status.php
```

## ⚙️ 技术细节

### 数据匹配逻辑
1. 使用`posting_number`作为主要匹配字段
2. 如果JSON中包含`store_id`，会同时匹配店铺ID以提高准确性
3. 只有当新状态与现有状态不同时才执行更新

### 更新字段
- `status` - 订单状态
- `updated_at` - 更新时间（自动设置为当前时间）

### 安全特性
- 事务处理确保数据一致性
- 详细的错误处理和日志记录
- 防止重复更新相同状态
- 支持调试模式查看详细信息

## 🛠️ 故障排查

### 常见问题

1. **找不到JSON文件**
   ```bash
   # 检查data目录是否存在
   ls -la worker/data/
   
   # 查看可用的JSON文件
   php update_order_status.php list
   ```

2. **订单不存在**
   - 可能是订单数据还没有导入到`ozon_order`表
   - 检查`posting_number`字段是否匹配

3. **数据库连接问题**
   - 检查`config.php`中的数据库配置
   - 确认数据库服务是否正常运行

### 调试模式
使用`debug`参数可以看到详细的处理信息：
```bash
php update_order_status.php all debug
```

## 📁 文件结构

更新工具会处理以下类型的文件：
```
worker/data/
├── 37_delivered_90days.json     ✓ 会处理
├── 38_awaiting_packaging_7days.json  ✓ 会处理
├── all_stores_delivered_90days.json  ✗ 跳过（合并文件）
└── custom_sync_result.json      ✓ 会处理
```

## 💡 最佳实践

1. **定期更新**: 建议在订单同步后立即运行状态更新
2. **监控日志**: 注意观察更新统计，及时发现异常
3. **备份数据**: 在大批量更新前建议备份数据库
4. **分批处理**: 对于大量数据，可以分批次处理避免超时

## 🕒 定时任务示例

```bash
# 每小时同步并更新订单状态
0 * * * * cd /path/to/worker && php batch_sync.php preset daily && php quick_update_status.php

# 每天凌晨进行全量更新
0 2 * * * cd /path/to/worker && php batch_sync.php preset weekly && php quick_update_status.php
```