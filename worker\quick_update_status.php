<?php
/**
 * 简化版订单状态更新脚本
 * 一键更新data目录下所有JSON文件中的订单状态到数据库
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

echo "=== 一键订单状态更新工具 ===\n";
echo "正在扫描data目录...\n\n";

$dataDir = __DIR__ . '/data';
if (!is_dir($dataDir)) {
    echo "错误: data目录不存在\n";
    exit(1);
}

// 获取所有JSON文件（排除合并文件）
$jsonFiles = glob($dataDir . '/*.json');
$jsonFiles = array_filter($jsonFiles, function($file) {
    return !str_contains(basename($file), 'all_stores_');
});

if (empty($jsonFiles)) {
    echo "data目录下没有找到JSON文件\n";
    exit(1);
}

echo "找到 " . count($jsonFiles) . " 个JSON文件:\n";
foreach ($jsonFiles as $file) {
    echo "- " . basename($file) . "\n";
}

echo "\n开始处理...\n";
echo str_repeat('=', 60) . "\n";

$totalStats = [
    'files' => 0,
    'processed' => 0,
    'updated' => 0,
    'errors' => 0,
    'not_found' => 0,
    'no_change' => 0
];

$statusChanges = [];

foreach ($jsonFiles as $filepath) {
    $filename = basename($filepath);
    echo "\n处理文件: {$filename}\n";
    
    // 读取JSON数据
    $content = file_get_contents($filepath);
    if ($content === false) {
        echo "  ✗ 无法读取文件\n";
        $totalStats['errors']++;
        continue;
    }
    
    $orders = json_decode($content, true);
    if ($orders === null) {
        echo "  ✗ JSON格式无效\n";
        $totalStats['errors']++;
        continue;
    }
    
    if (empty($orders)) {
        echo "  ℹ 文件为空，跳过\n";
        continue;
    }
    
    echo "  订单数量: " . count($orders) . "\n";
    $totalStats['files']++;
    
    $fileStats = [
        'processed' => 0,
        'updated' => 0,
        'errors' => 0,
        'not_found' => 0,
        'no_change' => 0
    ];
    
    // 处理每个订单
    foreach ($orders as $order) {
        if (!isset($order['posting_number']) || !isset($order['status'])) {
            $fileStats['errors']++;
            continue;
        }
        
        $postingNumber = $order['posting_number'];
        $newStatus = $order['status'];
        $storeId = $order['store_id'] ?? null;
        
        $fileStats['processed']++;
        
        try {
            // 构建查询条件 - 注意：数据库表中店铺字段是 storeid，不是 store_id
            $conditions = ['posting_number' => $postingNumber];
            if ($storeId !== null) {
                $conditions['storeid'] = $storeId;  // 使用 storeid 而不是 store_id
            }
            
            // 检查订单是否存在
            $existingOrder = $DB->find('ozon_order', ['id', 'status'], $conditions);
            
            if (!$existingOrder) {
                $fileStats['not_found']++;
                continue;
            }
            
            $oldStatus = $existingOrder['status'];
            
            // 检查状态是否需要更新
            if ($oldStatus === $newStatus) {
                $fileStats['no_change']++;
                continue;
            }
            
            // 更新状态
            $updateResult = $DB->update('ozon_order', 
                ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')], 
                $conditions
            );
            
            if ($updateResult) {
                $fileStats['updated']++;
                
                // 记录状态变更统计
                $changeKey = $oldStatus . ' → ' . $newStatus;
                if (!isset($statusChanges[$changeKey])) {
                    $statusChanges[$changeKey] = 0;
                }
                $statusChanges[$changeKey]++;
                
                echo "  ✓ {$postingNumber}: {$oldStatus} → {$newStatus}\n";
            } else {
                $fileStats['errors']++;
                echo "  ✗ {$postingNumber}: 更新失败\n";
            }
            
        } catch (Exception $e) {
            $fileStats['errors']++;
            echo "  ✗ {$postingNumber}: " . $e->getMessage() . "\n";
        }
    }
    
    // 累计统计
    foreach ($fileStats as $key => $value) {
        $totalStats[$key] += $value;
    }
    
    // 显示文件处理结果
    echo "  结果: 处理{$fileStats['processed']} 更新{$fileStats['updated']} 错误{$fileStats['errors']} 不存在{$fileStats['not_found']} 无需更新{$fileStats['no_change']}\n";
}

// 最终统计报告
echo "\n" . str_repeat('=', 60) . "\n";
echo "更新完成!\n\n";

echo "总体统计:\n";
echo "  处理文件: {$totalStats['files']}\n";
echo "  总订单数: {$totalStats['processed']}\n";
echo "  更新成功: {$totalStats['updated']}\n";
echo "  无需更新: {$totalStats['no_change']}\n";
echo "  订单不存在: {$totalStats['not_found']}\n";
echo "  处理错误: {$totalStats['errors']}\n";

if (!empty($statusChanges)) {
    echo "\n状态变更统计:\n";
    arsort($statusChanges); // 按数量降序排列
    foreach ($statusChanges as $change => $count) {
        echo "  {$change}: {$count} 个\n";
    }
}

echo "\n" . ($totalStats['updated'] > 0 ? "✅ 成功更新了 {$totalStats['updated']} 条订单状态!" : "ℹ️  没有订单状态需要更新") . "\n";

// 返回适当的退出码
exit($totalStats['errors'] > 0 ? 1 : 0);