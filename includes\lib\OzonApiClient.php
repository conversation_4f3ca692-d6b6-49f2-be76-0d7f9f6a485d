<?php
namespace lib;
use InvalidArgumentException;

class OzonApiClient {
    const BASE_URL = 'https://api-seller.ozon.ru';
    const MAX_OFFSET = 100000;
    const MAX_DATE_RANGE = 366;
    const STATUS_MAP = [
        'awaiting_packaging' => [
            'api_value' => 'awaiting_packaging',
            'description' => '等待打包'
        ],
        'awaiting_deliver' => [
            'api_value' => 'awaiting_deliver',
            'description' => '等待发货'
        ],
        'delivering' => [
            'api_value' => 'delivering',
            'description' => '运输中'
        ],
        'delivered' => [
            'api_value' => 'delivered',
            'description' => '已送达'
        ],
        'cancelled' => [
            'api_value' => 'cancelled',
            'description' => '已取消'
        ]
    ];

    private $clientId;
    private $apiKey;
    private $timeout = 25;
    private $maxRetries = 3;
    private $retryDelay = 1;
    private $proxy_server = '**************';
    private $proxy_port = 44389;
    private $proxy_userpwd = 'IIg5y6sTfD:IgqDFFHqcH';
    private $proxy;
    private $Redis;
    

    public function __construct( $clientId='2763302', $apiKey='0a1f6874-fe81-4b30-97ce-4a3339eb077e', $proxy=false) {
        $this->clientId = $clientId;
        $this->apiKey = $apiKey;
        $this->proxy = $proxy;
        $this->Redis = new \Redis();
    }
    
    #仓库清单
    public function Ozonwarehouses(){
        $url = 'https://api-seller.ozon.ru/v1/warehouse/list';
        $res = $this->curl($url,'POST');
        $res = json_decode($res,true);
        foreach ($res['result'] as $items){
            if($items['status']=='created'){
                $data[] = ['id'=>$items['warehouse_id'],'name'=>$items['name']];
            } 
        }
        if($data){
            return $data;
        }
        return false;
    }
       
    /**
     * 获取财务交易数据
     * @param array $requestData 请求数据
     * @return array|false
     */
    public function getFinanceTransactions($requestData) {
        $url = 'https://api-seller.ozon.ru/v3/finance/transaction/list';
        $response = $this->curl($url, 'POST', $requestData);
        return json_decode($response, true);
    }
    
        /**
     * 根据商品ID、offer_id或SKU批量获取商品属性
     * @param array $filters 包含 product_id, offer_id, 或 sku 数组的关联数组
     * @param int $limit
     * @param string $sortDir
     * @return array
     */
    public function getProductAttributes(array $filters, int $limit = 100, string $sortDir = 'ASC') {
        $url = self::BASE_URL . '/v4/product/info/attributes';
        
        $validFilters = [];
        if (!empty($filters['product_id'])) {
            $validFilters['product_id'] = $filters['product_id'];
        }
        if (!empty($filters['offer_id'])) {
            $validFilters['offer_id'] = $filters['offer_id'];
        }
        if (!empty($filters['sku'])) {
            $validFilters['sku'] = $filters['sku'];
        }

        if (empty($validFilters)) {
            return ['error' => 'At least one filter (product_id, offer_id, sku) is required.'];
        }
        
        $validFilters['visibility'] = $filters['visibility'] ?? 'ALL';

        $payload = [
            'filter' => $validFilters,
            'limit' => $limit,
            'sort_dir' => $this->validateSortDir($sortDir)
        ];

        $response = $this->curl($url, 'POST', $payload);
        $decoded = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'JSON decode failed: ' . json_last_error_msg(), 'raw_response' => $response];
        }

        return $decoded;
    }
      
       
    // ========== 聊天API方法 ==========
    
    /**
     * 获取聊天列表
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function getChatList($limit = 100, $offset = 0) {
        $url = self::BASE_URL . '/v2/chat/list';
        $data = [
            'limit' => $limit,
            'offset' => $offset
        ];
        
        error_log("Chat List API 请求: URL=$url, 参数=" . json_encode($data));
        
        $response = $this->curl($url, 'POST', $data);
        
        error_log("Chat List API 原始响应: " . var_export($response, true));
        
        // 检查是否有cURL错误
        if (is_array($response) && isset($response['error'])) {
            error_log("Chat List API cURL错误: " . $response['error']);
            return ['error' => $response['error']];
        }
        
        // 检查响应是否为空
        if (empty($response)) {
            error_log("Chat List API 响应为空");
            return ['error' => 'Empty response from API'];
        }
        
        $decoded = json_decode($response, true);
        
        // 检查JSON解码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Chat List API JSON解码失败: " . json_last_error_msg());
            error_log("原始响应内容: " . $response);
            return ['error' => 'JSON decode failed: ' . json_last_error_msg(), 'raw_response' => $response];
        }
        
        // 如果解码成功但结果为null
        if ($decoded === null) {
            error_log("Chat List API JSON解码结果为null");
            return ['error' => 'Decoded JSON is null', 'raw_response' => $response];
        }
        
        error_log("Chat List API 解码后响应: " . json_encode($decoded));
        return $decoded;
    }
    
    /**
     * 获取聊天历史消息
     * @param string $chatId 聊天ID
     * @param int $limit 限制数量
     * @param int $offset 偏移量
     * @return array
     */
    public function getChatHistory($chatId, $limit = 100, $offset = 0) {
        // 根据Ozon官方文档，聊天历史的正确端点
        $url = self::BASE_URL . '/v3/chat/history';
        $data = [
            'chat_id' => $chatId,
            'limit' => $limit,
            'offset' => $offset
        ];
        
        // 详细日志记录
        error_log("Chat History API 请求: URL=$url");
        error_log("Chat History API 参数: " . json_encode($data));
        error_log("Chat History API 客户端: ClientId={$this->clientId}");
        
        $response = $this->curl($url, 'POST', $data);
        
        // 记录原始响应
        error_log("Chat History API 原始响应: " . var_export($response, true));
        
        // 检查是否有cURL错误
        if (is_array($response) && isset($response['error'])) {
            error_log("Chat History API cURL错误: " . $response['error']);
            return ['error' => $response['error']];
        }
        
        // 检查响应是否为空
        if (empty($response)) {
            error_log("Chat History API 响应为空");
            return ['error' => 'Empty response from API'];
        }
        
        $decoded = json_decode($response, true);
        
        // 检查JSON解码是否成功
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Chat History API JSON解码失败: " . json_last_error_msg());
            error_log("原始响应内容: " . $response);
            return ['error' => 'JSON decode failed: ' . json_last_error_msg(), 'raw_response' => $response];
        }
        
        // 如果解码成功但结果为null
        if ($decoded === null) {
            error_log("Chat History API JSON解码结果为null");
            return ['error' => 'Decoded JSON is null', 'raw_response' => $response];
        }
        
        error_log("Chat History API 解码后响应: " . json_encode($decoded));
        return $decoded;
    }
    
    /**
     * 发送消息
     * @param string $chatId 聊天ID
     * @param string $text 消息内容
     * @param array $attachments 附件
     * @return array
     */
    public function sendMessage($chatId, $text, $attachments = []) {
        $url = self::BASE_URL . '/v1/chat/send/message';
        $data = [
            'chat_id' => $chatId,
            'text' => $text
        ];
        if (!empty($attachments)) {
            $data['attachments'] = $attachments;
        }
        $response = $this->curl($url, 'POST', $data);
        return json_decode($response, true);
    }
    
    /**
     * 标记消息为已读
     * @param string $chatId 聊天ID
     * @return array
     */
    public function markChatRead($chatId) {
        $url = self::BASE_URL . '/v2/chat/read';
        $data = [
            'chat_id' => $chatId
        ];
        $response = $this->curl($url, 'POST', $data);
        return json_decode($response, true);
    }
    
    /**
     * 发送文件
     * @param string $chatId 聊天ID
     * @param string $fileName 文件名
     * @param string $fileContent 文件内容(base64编码)
     * @return array
     */
    public function sendFile($chatId, $fileName, $fileContent) {
        $url = self::BASE_URL . '/v1/chat/send/file';
        $data = [
            'chat_id' => $chatId,
            'name' => $fileName,
            'content' => $fileContent
        ];
        $response = $this->curl($url, 'POST', $data);
        return json_decode($response, true);
    }
    
    /**
     * 获取聊天更新
     * @param string $since 从什么时间开始获取更新
     * @return array
     */
    public function getChatUpdates($since = null) {
        $url = self::BASE_URL . '/v1/chat/updates';
        $data = [];
        if ($since) {
            $data['since'] = $since;
        }
        $response = $this->curl($url, 'POST', $data);
        return json_decode($response, true);
    }
    
    // ========== 聊天API方法结束 ==========
    
    
    #复制同行商品
    public function OzonApiskufz(array $item){
        $url = 'https://api-seller.ozon.ru/v1/product/import-by-sku';
        $price = rand(100, 5000);
        $items[] = [
            'sku'             => $item['sku'],
            'name'            => '',
            'offer_id'        => $item['offer_id2'],
            'currency_code'   => "RUB",
            'old_price' => "$price",
            'price' => "-$price",
            'vat' => '0'
        ];
        $res = $this->curl($url,'POST',['items'=>$items]);
        //file_put_contents('OzonApiskufz.txt', "准备中\n".json_encode(['items'=>$items]) . "\n\n". $res. "\n\n", FILE_APPEND);
        $json = json_decode($res,true);
        return $json;
    }
    
    #获取商品特征描述
    public function characteristics($items=NULL){
        $url = 'https://api-seller.ozon.ru/v4/product/info/attributes';
        if($items['offer_id'] or $items['offer_id2']){
            $data['offer_id'][] =$items['offer_id2']?$items['offer_id2']:$items['offer_id'];
        }
        if($items['status']=='重新上架'){
            $data['sku'][] = $items['sku'];
        }
        $data['visibility'] =$items['visibility']?$items['visibility']:'ALL';
        $array = [
            'filter'=>$data,
            'limit'=>100,
        ];
        if($items['last_id']){
            $array['last_id']=$items['last_id'];
        }
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $res;
        }
        return $json['code']?false:$json;
    }
    
    #获取商品特征描述
    public function characteristics2($items=NULL){
        $url = 'https://api-seller.ozon.ru/v4/product/info/attributes';
        $data['sku'][] = $items['sku'];
        $data['visibility'] =$items['visibility']?$items['visibility']:'ALL';
        $array = [
            'filter'=>$data,
            'limit'=>100,
        ];
        if($items['last_id']){
            $array['last_id']=$items['last_id'];
        }
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        return $json['code']?false:$json;
    }
    
    #
    public function description_category_id(){
        $url = 'https://api-seller.ozon.ru/v1/description-category/tree';
        $array = ['language'=>'ZH_HANS'];
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        return $json;
    }
    
    #创建商品
    public function productimportgm(array $row){
        $url = 'https://api-seller.ozon.ru/v3/product/import';
        $attributes = json_decode($row['return'],true);
        $pt = false;
        foreach ($attributes['attributes'] as $pppq){
            if($pppq['id']==85){
                $pt = true;
            }
        }
        if(!$pt){
            $attributes['attributes'][]=[
                'id'=>85,
                'complex_id'=>0,
                'values'=>[['dictionary_value_id'=>126745801,'value'=>'Нет бренда']]
            ];
        }
        $items[] = [
            'attributes'=>$attributes['attributes'],                           #特征描述
            'description_category_id'=>$row['description_category_id'],        #类别ID
            //'color_image'=>'',                                               #营销色彩
            'currency_code'=>$row['currency_code'],                            #价格显示的货币
            'depth'=>$row['depth'],                                            #包装厚度
            'width'=>$row['width'],                                            #包装宽度
            'weight'=>$row['weight'],                                          #含包装的商品重量
            'weight_unit'=>'g',                                                #测重单位
            'dimension_unit'=>'mm',                                            #尺寸测量单位
            'height'=>$row['height'],                                          #包装高度。
            //'primary_image'=>$row['primary_image'],                          #商品主图
            'name'=>$row['title'],                                             #商品名称
            'offer_id'=>$row['offer_id'],                                      #货号
            'old_price'=>$row['price'],                                        #折扣前的价格（将在产品卡上划掉）
            //'pdf_list'=>'',                                                  #PDF-文件清单。
            'price'=>$row['price'],                                            #商品价格，包括折扣
            'type_id'=>$row['type_id'],                                        #商品类型的标识符
            'vat'=>'0',                                                        #商品增值税税率
        ];
        if($row['barcode'])$items[0]['barcode']=$row['barcode'];               #商品条码
        $res = $this->curl($url,'POST',$this->filterDuplicates(['items'=>$items]));
        $json = json_decode($res,true);
        if($json['result']['task_id'])return [$json['result']['task_id'],$items[0]['offer_id']];
        return $res;
    }
    
    #采集上架
    public function productimports(array $row,array $attributes){
        $url = 'https://api-seller.ozon.ru/v3/product/import';
        $pt = false;
        $items[] = [
            'attributes'=>$attributes,                           #特征描述
            'description_category_id'=>$row['category2'],        #类别ID
            'currency_code'=>$row['currency_code'],                            #价格显示的货币
            'depth'=>$row['depth'],                                            #包装厚度
            'width'=>$row['width'],                                            #包装宽度
            'weight'=>$row['weight'],                                          #含包装的商品重量
            'weight_unit'=>'g',                                                #测重单位
            'dimension_unit'=>'mm',                                            #尺寸测量单位
            'height'=>$row['height'],                                          #包装高度。
            'name'=>$row['title'],                                             #商品名称
            'offer_id'=>$row['offer_id'],                                      #货号
            'old_price'=>$row['price'],                                        #折扣前的价格（将在产品卡上划掉）
            'price'=>$row['price'],                                            #商品价格，包括折扣
            'type_id'=>$row['category3'],                                        #商品类型的标识符
            'vat'=>'0',                                                        #商品增值税税率
        ];
        if($row['barcode'])$items[0]['barcode']=$row['barcode'];               #商品条码
        $res = $this->curl($url,'POST',$this->filterDuplicates(['items'=>$items]));
        $json = json_decode($res,true);
        return $json;
    }
    
    #创建商品
    public function productimport(array $item){
        $url = 'https://api-seller.ozon.ru/v3/product/import';
        
        $attributes = json_decode($item['return'],true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        $complex_attributes[]['attributes']=$attributes['complex_attributes'];
        
        $attributes['attributes'] = array_filter($attributes['attributes']??[], function($itemax) {
            return $itemax['id'] != 85;
        });
        $attributes['attributes'] = array_filter($attributes['attributes']??[], function($itemax) {
            return $itemax['id'] != 4195;
        });
        $attributes['attributes'][]=[
            'id'=>85,
            'complex_id'=>0,
            'values'=>[['dictionary_value_id'=>126745801,'value'=>'Нет бренда']]
        ];
        $attributes['attributes'] = array_values($attributes['attributes']);
        if(empty($attributes['weight']) or empty($attributes['depth'])){
            return false;
        }
        $redis = $this->Redis;
        $redis->connect('127.0.0.1', 6379);
        $rsdisplay = $redis->get($item['sku'].'_'.$item['ClientId'].'_Watermark');
        $redis->close();
        if(isset($rsdisplay)){
            $json = json_decode($rsdisplay,true);
            if(isset($json['images'])){
                $attributes['images'] = $json['images'];
            }
            if(isset($json['primary_image'])){
                $attributes['primary_image'] = $json['primary_image'];
            }
        }
        $items[] = [
            'attributes'=>$attributes['attributes'],                                    #特征描述
            'barcode'=>$attributes['barcode']??"",                                      #商品条码
            'description_category_id'=>$attributes['description_category_id'],          #类别ID
            'color_image'=>$attributes['color_image'],                                  #营销色彩
            'currency_code'=>$item['currency_code']?$item['currency_code']:'CNY',       #价格显示的货币
            'depth'=>intval($attributes['depth']),                                      #包装厚度
            'width'=>intval($attributes['width']),                                      #包装宽度
            'weight'=>round($attributes['weight']),                                     #含包装的商品重量
            'weight_unit'=>$attributes['weight_unit']?$attributes['weight_unit']:'g',   #测重单位
            'dimension_unit'=>'mm',                                                     #尺寸测量单位
            'height'=>intval($attributes['height']),                                    #包装高度。
            'primary_image'=>$attributes['primary_image'],                              #商品主图
            'promotions'=>[['operation'=>'DISABLE','type'=>'REVIEWS_PROMO']],           #促销活动。
            'images'=>$attributes['images'],                                            #图像组
            'complex_attributes'=>$complex_attributes,                                  #上传视频
            'name'=>$item['title']??"",                                                 #商品名称
            'offer_id'=>$item['offer_id'],                                              #货号
            'old_price'=>(string)$item['old_price'],                                    #折扣前的价格（将在产品卡上划掉）
            'pdf_list'=>$attributes['pdf_list']??'',                                    #PDF-文件清单。
            'price'=>$item['price'],                                                    #商品价格，包括折扣
            'type_id'=>intval($attributes['type_id']),                                  #商品类型的标识符
            'vat'=>'0',                                                                 #商品增值税税率
        ];
        //exit(json_encode(['items'=>$items]));
        $res = $this->curl($url,'POST',['items'=>$items]);
        $json = json_decode($res,true);
        if($json['result']['task_id'])return [$json['result']['task_id'],$items[0]['offer_id']];
        file_put_contents('创建商品.txt', "创建商品\n".$item['offer_id'] ."\n\n".json_encode(['items'=>$items], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES). "\n\n". $res. "\n\n", FILE_APPEND);
        return $json;
    }
    
    #查询商品添加或更新状态
    public function productimportinfo($row){
        $url = 'https://api-seller.ozon.ru/v1/product/import/info';
        $res = $this->curl($url,'POST',['task_id'=>$row['task_id']]);
        $json = json_decode($res,true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $res;
        }
        if($json['result']['items'][0]){
            file_put_contents('添加或更新状态.txt', "更新状态\n".$row['task_id'] ."\n\n".json_encode(['task_id'=>$row['task_id']]). "\n\n". $res. "\n\n", FILE_APPEND);
        }
        return $json['result']['items'][0]?$json['result']['items'][0]:$json;
    }
    
    
    #关于卖家库存余额的信息
    public function stockswarehouse($items,$type='sku'){
        $url = "https://api-seller.ozon.ru/v1/product/info/stocks-by-warehouse/fbs";
        $res = $this->curl($url,'POST',[$type=>[$items]]);
        $json = json_decode($res,true);
        if($json['result'])return $json['result'];
        return false;
    }
    
    #关于商品数量的信息
    public function productinfostocks($item,$type=0){
        $url = 'https://api-seller.ozon.ru/v4/product/info/stocks';
        $array = [];
        if($type===0){
            if(is_array($item) && isset($item['offer_id'])){
                $array['filter']['offer_id'][] = $item['offer_id'];
            }
            $array['limit'] = 10;
        }else{
            $array = $item;
        }
        
        $res = $this->curl($url,'POST', $array);
        $json = json_decode($res,true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        if(isset($json['code'])===3){
            error_log(json_encode($array));
            error_log(json_encode($json));
        }
        return $json;
    }
    
    #商品仓库添加库存
    public function productsstocks(array $item, $type='offer_id', $stock = false, $array = false){
        $url = 'https://api-seller.ozon.ru/v2/products/stocks';
        if($type!='offer_id'){
            $id = $item['product_id'];
            if($stock and $array==false){
                $item['stock'] = 0;
            }
        }else{
            $id = $item['offer_id'];
        }
        if($array){
            $items = $item;
        }else{
            $items[] = [
                $type=>$id,
                'stock'=>(int)$item['stock'],
                'warehouse_id'=>(int)$item['warehouse_id']
            ];
        }
        
        $res = $this->curl($url,'POST',['stocks'=>$items]);
        $json = json_decode($res,true);
        return $json;
    }
    
    #商品仓库添加库存
    public function productsstocksarray(array $item){
        $url = 'https://api-seller.ozon.ru/v2/products/stocks';
        $res = $this->curl($url,'POST',['stocks'=>$item]);
        $json = json_decode($res,true);
        return $json;
    }
    
    #商品列表
    public function productlist(array $item){
        $url = 'https://api-seller.ozon.ru/v3/product/list';
        $array = [
            'filter'=>['visibility'=>$item['visibility']],
            'last_id'=>$item['last_id']?$item['last_id']:'',
            'limit'=>$item['limit']?$item['limit']:1000
        ];
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        return $json?$json:false;
    }
    
    #更新价格
    public function importprices($item, $auto_action_enabled = true){
        $url = 'https://api-seller.ozon.ru/v1/product/import/prices';
        $array = [
            'auto_action_enabled'=>$auto_action_enabled?'DISABLED':'UNKNOWN',
            'currency_code'=>$item['currency_code'],
              'product_id'=>intval($item['product_id']),
            'price'=>$item['price'],
            'vat'=>'0'
        ];
        if($item['min_price']>=1)$array['min_price'] = $item['min_price']; #应用促销活动后的商品最低价格。
        $netPrice = $item['net_price'] ?? null;
        if (is_numeric($netPrice) && $netPrice >= 1) {
            $array['net_price'] = $netPrice; #产品成本价。
        }
        if($item['old_price']>=1)$array['old_price'] = $item['old_price']; #折扣前的价格（在商品卡上划掉）
        $res = $this->curl($url,'POST',['prices'=>[$array]]);
        $json = json_decode($res,true);
        return $json['result'][0]['updated']?true:false;
    }
    
    
     #创建或更新商品 (使用V3接口)
    public function productimportV2(array $items){
        $url = 'https://api-seller.ozon.ru/v3/product/import';
        $res = $this->curl($url,'POST',['items'=>$items]);
        $json = json_decode($res,true);
        return $json;
    }
    
    #更新商品特征
    public function attributesupdate($row, $array){
        $url = 'https://api-seller.ozon.ru/v1/product/attributes/update';
        /*
        $json = json_decode($row['return'],true);
        foreach ($json['images'] as $img){
            $values[]=['dictionary_value_id'=>0,'value'=>$img];
        }
        $attributes[] = ['id'=>4195,'complex_id'=>0,'values'=>$values,''];
        $array[] = ['attributes'=>$attributes,'offer_id'=>$row['offer_id']];
        */
        $res = $this->curl($url,'POST',['items'=>$array]);
        $json = json_decode($res,true);
        return $json['task_id']?$json['task_id']:false;
    }
    
    #更新图片特征
    public function attributesimagesupdate($row){
        $url = 'https://api-seller.ozon.ru/v1/product/attributes/update';
        $json = json_decode($row['return'],true);
        foreach ($json['images'] as $img){
            $values[]=['dictionary_value_id'=>0,'value'=>$img];
        }
        $attributes[] = ['id'=>4195,'complex_id'=>0,'values'=>$values,''];
        $array[] = ['attributes'=>$attributes,'offer_id'=>$row['offer_id']];
        $res = $this->curl($url,'POST',['items'=>$array]);
        $json = json_decode($res,true);
        return $json['task_id']?$json['task_id']:false;
    }
    
    # 根据标识符获取产品列表
    public function productinfolist($item,$type='product_id'){
        $url = 'https://api-seller.ozon.ru/v3/product/info/list';
        $items = [$type=>$item];
        $res = $this->curl($url,'POST',$items);
        return $res;
    }
    
    #将商品归档
    public function productarchive(array $item){
        $url = 'https://api-seller.ozon.ru/v1/product/archive';
        $res = $this->curl($url,'POST',['product_id'=>$item]);
        $json = json_decode($res,true);
        return $json['result']?$json['result']:false;
    }
    
    #从存档删除没有SKU的商品
    public function productsdelete(array $item){
        $url = 'https://api-seller.ozon.ru/v2/products/delete';
        $res = $this->curl($url,'POST',['products'=>$item]);
        $json = json_decode($res,true);
        return $json['result']?$json['result']:false;
    }
    
    //商品类别和类型的树形图
    public function tree($item='ZH_HANS'){
        $url = 'https://api-seller.ozon.ru/v1/description-category/tree';
        $res = $this->curl($url,'POST',['language'=>$item]);
        $json = json_decode($res,true);
        return $json['result']?$json['result']:false;
    }
    
    #类别特征列表
    public function attribute($row){
        $url = "https://api-seller.ozon.ru/v1/description-category/attribute";
        $items = [
            'description_category_id'=>$row['description_category_id'],
            'language'=>$row['language']??'ZH_HANS',
            'type_id'=>$row['type_id'],
        ];
        $res = $this->curl($url,'POST',$items);
        $json = json_decode($res,true);
        return $json['result']?$json['result']:false;
    }
    
    #特征值指南
    public function attributevalues($item,$test=false){
        $items = [
            'attribute_id'=>$item['attribute_id'],
            'description_category_id'=>$item['categoryId']?$item['categoryId']:$item['description_category_id'],
            'language'=>$item['language']?$item['language']:'ZH_HANS',
            'last_value_id'=>$item['last_value_id']?$item['last_value_id']:0,
            'limit'=>$item['limit']?$item['limit']:2000,
            'type_id'=>$item['type_id']
        ];
        $url = 'https://api-seller.ozon.ru/v1/description-category/attribute/values';
        $res = $this->curl($url,'POST',$items);
        
        $json = json_decode($res,true);
        if($test){
            if($json['msg'])return 0;
            foreach($json['result'] as $item){
                if($item['value']==$test){
                    return $item['id']?$item['id']:0;
                }
            }
            return 0;
        }else {
            return $json['result']?$json['result']:false;
        }
    }
    
    public function attributesearch($item){
        $url = 'https://api-seller.ozon.ru/v1/description-category/attribute/values/search';
        $items = [
            'attribute_id'=>$item['attribute_id'],
            'description_category_id'=>$item['categoryId'],
            'limit'=>$item['limit']?$item['limit']:100,
            'type_id'=>$item['type_id'],
            'value'=>$item['value']
        ];
        $res = $this->curl($url,'POST',$items);
        $json = json_decode($res,true);
        return $json['result'][0]['id']?$json['result'][0]['id']:0;
    }
    
    #按照ID获取货件信息
    public function postingfbsget($posting_number,$x=false){
        $url = "https://api-seller.ozon.ru/v3/posting/fbs/get";
        $items = [
            'posting_number'=>$posting_number,
            'with'=>[
                'analytics_data'=>$x,
                'barcodes'=>$x,
                'financial_data'=>true,
                'legal_info'=>$x,
                'product_exemplars'=>$x,
                'related_postings'=>$x,
                'translit'=>$x
            ]
        ];
        $res = $this->curl($url,'POST',$items);
        $json = json_decode($res,true);
        return $json;
    }
    
    #品类限制、商品的创建和更新
    public function productinfolimit($row=null,$echo = false){
        global $DB;
        $url = "https://api-seller.ozon.ru/v4/product/info/limit";
        $res = $this->curl($url,'POST');
        $json = json_decode($res,true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        if($echo){
            return $json;
        }
        if($json['daily_create'] and $json['daily_update']){
            $daily_create = $json['daily_create']['limit']-$json['daily_create']['usage'];
            $daily_update = $json['daily_update']['limit']-$json['daily_update']['usage'];
            $limit        = $json['total']['limit']-$json['total']['usage'];
            $DB->update('store', ['daily_create'=>$daily_create,'daily_update'=>$daily_update,'limit'=>$limit], ['id'=>$row['storeid']?$row['storeid']:$row['id']]);
        }
        return true;
    }
    
    
    #活动清单
    public function v1actions(){
        $url = "https://api-seller.ozon.ru/v1/actions";
        $res = $this->curl($url,'GET');
        $json = json_decode($res,true);
        return $json;
    }
    
    #参与 活动的商品列表
    public function actionsproducts(){
        $url = 'https://api-seller.ozon.ru/v1/actions/products';
        $data = $this->v1actions();
        
        foreach ($data['result'] as $item){
            $array = ['action_id'=>$item['id'],'limit'=>100];
            $res = $this->curl($url,'POST',$array);
            $json = json_decode($res,true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }
            if($json['result']['total']>0){
                foreach ($json['result']['products'] as $item2){
                    if($item2['add_mode']=='MANUAL'){
                        continue;
                    }
                    $product_ids[] = $item2['id'];
                }
                $this->deactivate(['action_id'=>$item['id'],'product_ids'=>$product_ids]);
            }
        }
        return false; 
    }
    
     #删除活动
    public function deactivate($item){
        $url = "https://api-seller.ozon.ru/v1/actions/products/deactivate";
        $items = [
            'action_id'=>$item['action_id'],
            'product_ids'=>$item['product_ids']
        ];
        $res = $this->curl($url,'POST',$items);
        $json = json_decode($res,true);
        if($json['error']){
            return false;
        }
        if(isset($json['result']['product_ids']) && is_array($json['result']['product_ids'])){
            return true;
        }else{
            return false;
        }
    }
    
    #打印标签
    public function packagelabel($row){
        $url = "https://api-seller.ozon.ru/v2/posting/fbs/package-label";
        $posting_number[] = $row['posting_number'];
        $res = $this->curl($url,'POST',['posting_number'=>$posting_number]);
        if($res){
            $isPdf = substr($res, 0, 5) === '%PDF-';
            if (!$isPdf) {
                // 如果不是PDF，可以记录错误或返回特定错误信息
                return false;
                // 或者 return ['error' => '非PDF文件'];
            }
            $targetDateTime = $row['in_process_at'];
            $dateTime = new \DateTime($targetDateTime);
    
            // 步骤2：生成文件夹路径（格式：年/月/日）
            $year  = $dateTime->format('Y');
            $month = $dateTime->format('m');
            $day   = $dateTime->format('d');
            $dateFolder = $year.$month.$day;
            // 步骤3：创建文件夹（假设根目录为 uploads）
            $baseDir = ROOT . 'assets/order/';
            $targetDir = $baseDir . $dateFolder;
            if (!is_dir($targetDir)) {
                if (!mkdir($targetDir, 0775, true)) {
                    throw new Exception("无法创建文件夹: $targetDir");
                }
            }
            $urls = $targetDir.'/'.$row['posting_number'].'.pdf';
            
            if(file_put_contents($urls, $res)){
                chmod($urls, 0777);
                return '/assets/order/'.$dateFolder.'/'.$row['posting_number'].'.pdf';
            }
        }
        return false;
    }
    ##打印机标签
    public function packagelabelyun($row){
        $url = "https://api-seller.ozon.ru/v2/posting/fbs/package-label";
        $posting_number[] = $row['posting_number'];
        $res = $this->curl($url, 'POST', ['posting_number' => $posting_number]);
        
        // 尝试解析JSON响应
        $jsonResponse = json_decode($res, true);
        
        // 如果是有效的JSON则返回，否则返回原始响应或false
        return $jsonResponse ? $jsonResponse : (trim($res) ? $res : false);
    }
    
    #订单备货
    public function fbsshippackage($row){
        $url = 'https://api-seller.ozon.ru/v4/posting/fbs/ship/package';
        //$product_id = $this->related_sku($row['sku'])[0]['product_id'];
        if($row['products']){
            $row['products'] = json_decode($row['products'],true);
            foreach ($row['products'] as $item){
                $products[] = ['product_id'=>$item['sku'],'quantity'=>$item['quantity']];
            }
        }else{
            $products[] = ['product_id'=>$row['sku'],'quantity'=>$row['quantity']];
        }
        $array = [
            'posting_number'=>$row['posting_number'],
            'products'=>$products
        ];
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        if(count($json)){
            return true;
        }else{
            return $json;
        }
    }
    
    #订单备货
    public function fbsshippackages($array){
        $url = 'https://api-seller.ozon.ru/v4/posting/fbs/ship/package';
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        if(isset($json['result'])){
            return true;
        }else{
            return $json;
        }
    }
    
    public function related_sku($sku){
        $url = "https://api-seller.ozon.ru/v1/product/related-sku/get";
        $array = [
            'sku'=>["$sku"]
        ];
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        return $json['items'];
    }
    
    /**
     * 获取商品图片信息（使用 /v2/product/pictures/info API）
     * 
     * @param string|array $sku 商品的 SKU（可以是单个 SKU 或数组）
     * @return array|null 返回图片信息，失败返回 null
     */
    public function getProductImageInfo($sku) {
        $url = 'https://api-seller.ozon.ru//v2/product/pictures/info';
        
        // 确保 $sku 是数组格式（API 要求）
        $skuArray = is_array($sku) ? $sku : [$sku];
        
        // 构造请求数据
        $requestData = ['sku' => $skuArray];
        
        // 调用 API
        $response = $this->curl($url, 'POST', $requestData);
        exit($response);
        // 解析响应
        $data = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg());
            return null;
        }
        
        // 检查是否有错误
        if (isset($data['errors']) && !empty($data['errors'])) {
            error_log("API returned errors: " . json_encode($data['errors']));
            return null;
        }
        
        // 返回有效数据
        return $data['items'] ?? null;
    }
    
    # 退货申请列表
    public function returnsrfbslist($row){
        $url = 'https://api-seller.ozon.ru/v2/returns/rfbs/list';
        $array = [
            'filter'=>[
                'group_state'=>['ALL'],
                'created_at'=>[
                    'from'=>$this->parseDateStrings($row['from']),
                    'to'=>$this->parseDateStrings($row['to'])
                ]
            ],
            'limit'=>1000
        ];
        if($row['last_id']){
            $array['last_id'] = $row['last_id'];
        }
        $res = $this->curl($url,'POST',$array);
        exit($res);
    }
    
    public function fbsunfulfilledlist($row){
        $url = "https://api-seller.ozon.ru/v3/posting/fbs/unfulfilled/list";
        
        $array = [
            'dir'=>'ASC',
            'filter'=>[
                'cutoff_from'=>$this->parseDateStrings($row['cutoff_from']),
                'cutoff_to'=>$this->parseDateStrings($row['cutoff_to'])
            ],
            'limit'=>1000,
            'offset'=>0,
            'with'=>[
                'analytics_data'=>true,
                'barcodes'=>true,
                'financial_data'=>true,
                'translit'=>true,
            ]
        ];
        $res = $this->curl($url,'POST',$array);
        $json = json_decode($res,true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }
        return $json;
    }
    
    public function getFbsPostingListV3(array $filter, int $limit = 1000, int $offset = 0, string $sortDir = 'asc'): array {
        $url = 'https://api-seller.ozon.ru/v3/posting/fbs/list';
        $payload = [
            'dir' => $this->validateSortDir($sortDir),
            'filter' => $this->sanitizeFilter($filter),
            'limit' => max(1, min(1000, $limit)),
            'offset' => max(0, $offset),
            'with'=>[
                'financial_data'=>true
            ]
        ];
    
        $response = $this->curl($url, 'POST', $payload);
        
        if ($response === false || $response === null) {
            throw new RuntimeException("API request failed or returned empty response");
        }
    
        $decoded = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new RuntimeException("Failed to decode JSON response: " . json_last_error_msg());
        }
    
        if (!is_array($decoded)) {
            throw new RuntimeException("Unexpected API response format");
        }
    
        return $decoded;
    }
    
    public function getOzonProductsid($productFilters) {
    
        $validFilters = array_filter([
            'offer_id'   => $productFilters['offer_id'] ?? [],
            'product_id' => $productFilters['product_id'] ?? [],
            'sku'        => $productFilters['sku'] ?? []
        ], function($v) { return !empty($v); });
        
        if (empty($validFilters)) {
            return ['error' => '至少需要提供一个过滤参数（offer_id/product_id/sku）'];
        }
        
        // 准备请求数据
        $apiUrl = 'https://api-seller.ozon.ru/v3/product/info/list';
        $headers = [
            'Client-Id: ' .  $this->clientId,
            'Api-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $requestBody = json_encode($validFilters, JSON_UNESCAPED_UNICODE);
        // 初始化cURL
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => $apiUrl,
            CURLOPT_RETURNTRANSFER  => true,
            CURLOPT_POST           => true,
            CURLOPT_HTTPHEADER      => $headers,
            CURLOPT_POSTFIELDS      => $requestBody,
            CURLOPT_TIMEOUT         => 30,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_HTTP_VERSION   => CURL_HTTP_VERSION_1_1,
        ]);
        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        // 处理响应
        if ($response === false) {
            return ['error' => 'cURL请求失败：' . $curlError];
        }
        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['error' => 'JSON解析失败：' . json_last_error_msg()];
        }
        // 检查HTTP状态码
        if ($httpCode !== 200) {
            return [
                'error' => 'API请求失败',
                'http_code' => $httpCode,
                'response' => $decodedResponse
            ];
        }
        return [
            'success' => true,
            'data' => $decodedResponse
        ];
    }
    
    private function sanitizeFilter(array $filter): array {
        $sanitized = [];
        $hasSince = isset($filter['since']);
        $hasTo = isset($filter['to']);
        
        if ($hasSince || $hasTo) {
            if (!$hasSince || !$hasTo) {
                throw new InvalidArgumentException("必须同时提供since和to参数");
            }

            try {
                $sinceDate = $this->parseDateString($filter['since']);
                $toDate = $this->parseDateString($filter['to']);
                
                if ($sinceDate > $toDate) {
                    throw new InvalidArgumentException("结束时间不能早于开始时间");
                }
                
                $diffDays = $toDate->diff($sinceDate)->days;
                if ($diffDays > self::MAX_DATE_RANGE) {
                    throw new InvalidArgumentException("时间范围不能超过".self::MAX_DATE_RANGE."天");
                }

                $sanitized['since'] = $sinceDate->format(\DateTime::RFC3339);
                $sanitized['to'] = $toDate->format(\DateTime::RFC3339);

            } catch (Exception $e) {
                throw new InvalidArgumentException("时间参数错误: " . $e->getMessage());
            }
        }

        if (isset($filter['status'])) {
            if (!isset(self::STATUS_MAP[$filter['status']])) {
                $validKeys = implode(', ', array_keys(self::STATUS_MAP));
                throw new InvalidArgumentException(
                    "无效状态标识：'{$filter['status']}'。可用值：{$validKeys}"
                );
            }
            $sanitized['status'] = self::STATUS_MAP[$filter['status']]['api_value'];
        }

        return $sanitized;
    }

    private function parseDateString(string $dateStr): \DateTime {
        $formats = [
            \DateTime::RFC3339,
            'Y-m-d\TH:i:sP',
            'Y-m-d\TH:i:s',
            'Y-m-d H:i:s',
            'Y-m-d'
        ];

        foreach ($formats as $format) {
            $date = \DateTime::createFromFormat($format, $dateStr, new \DateTimeZone('UTC'));
            if ($date !== false) {
                if (strlen($dateStr) <= 10) {
                    $date->setTime(0, 0, 0);
                }
                return $date;
            }
        }
        throw new Exception("无法解析的日期格式: {$dateStr}");
    }

    private function validateSortDir(string $dir): string {
        $dir = strtolower(trim($dir));
        return in_array($dir, ['asc', 'desc']) ? $dir : 'asc';
    }

    private function enrichPostingData(array $posting): array {
        $products = is_array($posting['products'] ?? null) ? $posting['products'] : [];
        $total = 0;
        foreach ($products as $product) {
            $total += ($product['price'] ?? 0) * ($product['quantity'] ?? 1);
        }
        
        return [
            'posting_number' => (string)($posting['posting_number'] ?? '未知单号'),
            'status_text' => $this->mapStatus($posting['status'] ?? 'unknown'),
            'created_at' => $this->normalizeDate($posting['in_process_at'] ?? $posting['created_at'] ?? ''),
            'products' => $products,
            'financial' => [
                'total' => round($total, 2),
                'commission' => round($posting['commission']['amount'] ?? 0, 2)
            ]
        ];
    }

    private function mapStatus(string $status): string {
        return self::STATUS_MAP[$status]['description'] ?? '未知状态';
    }

    private function normalizeDate(string $date): string {
        try {
            return (new DateTime($date))->format('Y-m-d H:i:s');
        } catch (Exception $e) {
            return '1970-01-01 00:00:00';
        }
    }
    
    #offer_id生成函数
    public function generate_offer_id($length = 13) {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'; // 仅大写字母
        $id = '';
        for ($i = 0; $i < $length; $i++) {
            $id .= $chars[random_int(0, 25)]; // 从0-25中随机取索引
        }
        return $id;
    }
    
    private function curl($url, $method, $items=false, $proxy=false){
        $TIMEOUT = 20;
        if($url=='https://api-seller.ozon.ru/v3/product/info/list' or $url=='https://api-seller.ozon.ru/v3/product/list')$TIMEOUT = 120;
        $headers = [
            'Client-Id: ' . $this->clientId,
            'Api-Key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        // 初始化cURL
        $ch = curl_init();
        if($proxy or $this->proxy){
        	curl_setopt($ch, CURLOPT_PROXYAUTH, CURLAUTH_BASIC);
        	curl_setopt($ch, CURLOPT_PROXY, $this->proxy_server);
        	curl_setopt($ch, CURLOPT_PROXYPORT, $this->proxy_port);
        	curl_setopt($ch, CURLOPT_PROXYUSERPWD, $this->proxy_userpwd);
        	curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
        }
        curl_setopt_array($ch, [
            CURLOPT_CUSTOMREQUEST  => $method,
            CURLOPT_URL            => $url,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_RETURNTRANSFER => true,    // 返回响应内容
            CURLOPT_HEADER         => false,   // 不包含响应头
            CURLOPT_TIMEOUT        => $TIMEOUT?$TIMEOUT:20,       // 超时时间(秒)
            CURLOPT_CONNECTTIMEOUT => 30
        ]);
        if($items){
            curl_setopt($ch, CURLOPT_POSTFIELDS,json_encode($items,JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        }
        // 执行请求
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        // 错误处理
        if ($error) {
            return json_encode(['error' => "cURL Error: $error"]);
        }
        // 解析JSON响应
        return $response;
    }
    private function convertUrlsToArray($urlsString) {
        // 使用正则表达式处理不同系统的换行符（\r\n|\n|\r）
        $urls = preg_split('/\r\n|\n|\r/', $urlsString);
        
        // 过滤空值（如空行）
        $urls = array_filter($urls, function($url) {
            return !empty($url);
        });
        // 重新索引数组
        return array_values($urls);
    }
    
    
    private function optimizeAttribute11254(&$attr) {
        // 只处理目标属性
        if ($attr['id'] != 11254) return;
        $mergedContent = [
            'content' => [],
            'version' => 0
        ];
        
        // 合并所有values中的内容
        foreach ($attr['values'] as $valueObj) {
            $contentData = json_decode($valueObj['value'], true);
    
            if (json_last_error() === JSON_ERROR_NONE) {
                // 合并内容模块
                if (isset($contentData['content'])) {
                    $mergedContent['content'] = array_merge(
                        $mergedContent['content'],
                        $contentData['content']
                    );
                }
                // 保留最高版本号
                $mergedContent['version'] = max(
                    $mergedContent['version'],
                    $contentData['version'] ?? 0
                );
            }
        }
        
        // 去重图片区块（基于src去重）
        $seenImages = [];
        foreach ($mergedContent['content'] as &$widget) {
            if ($widget['widgetName'] === 'raShowcase') {
                $uniqueBlocks = [];
                foreach ($widget['blocks'] as $block) {
                    $src = $block['img']['src'] ?? null;
                    if ($src && !isset($seenImages[$src])) {
                        $seenImages[$src] = true;
                        $uniqueBlocks[] = $block;
                    }
                }
                $widget['blocks'] = $uniqueBlocks;
            }
        }
        // 更新优化后的数据
        $attr['values'] = [[
            'dictionary_value_id' => 0,
            'value' => json_encode($mergedContent, JSON_UNESCAPED_SLASHES)
        ]];
    }
    
    
    
    
    
    private function filterDuplicates($data) {
        $seenOfferIds = [];
        $uniqueItems = [];
        foreach ($data['items'] as $item) {
            // Attribute级别去重
            foreach ($item['attributes'] as $attr) {
                $attrId = $attr['id'];
                if (!isset($seenAttrIds[$attrId])) {
                    // 特殊处理11254属性
                    if ($attrId == 11254) {
                        $this->optimizeAttribute11254($attr);
                    }
                    $seenAttrIds[$attrId] = true;
                    $uniqueAttrs[] = $attr;
                }
            }
            
            $item['attributes'] = $uniqueAttrs;
            
            // 商品去重（修复语法错误）
            $offerId = $item['offer_id'];
            if (!isset($seenOfferIds[$offerId])) {
                $seenOfferIds[$offerId] = true;
                $uniqueItems[] = $item;
            }
        }
        return ['items' => $uniqueItems];
    }
    private function parseDateStrings($beijingTime = null) 
    {
        // 1. 设置北京时区 (UTC+8)
        $beijingTz = new \DateTimeZone('Asia/Shanghai');
        
        // 2. 如果未传入时间，使用当前北京时间
        $dateTime = $beijingTime 
            ? \DateTime::createFromFormat('Y-m-d H:i:s', $beijingTime, $beijingTz)
            : new \DateTime('now', $beijingTz);
        
        // 3. 转换为莫斯科时间 (UTC+3)
        $moscowTz = new \DateTimeZone('Europe/Moscow');
        $dateTime->setTimezone($moscowTz);
        
        // 4. 格式化为带毫秒的 ISO 格式
        list($microtime, $timestamp) = explode(' ', microtime());
        $milliseconds = (int) ($microtime * 1000);
        
        return $dateTime->format("Y-m-d\TH:i:s") . '.' . str_pad($milliseconds, 3, '0', STR_PAD_RIGHT) . 'Z';
    }
}

