<?php
/**
 * 数据库连接测试脚本
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "=== 数据库连接测试 ===\n";
echo "当前工作目录: " . getcwd() . "\n";
echo "脚本位置: " . __DIR__ . "\n";

// 尝试引入common.php
$commonPath = __DIR__ . '/../includes/common.php';
echo "Common.php路径: {$commonPath}\n";
echo "文件是否存在: " . (file_exists($commonPath) ? '是' : '否') . "\n";

if (!file_exists($commonPath)) {
    echo "错误: common.php文件不存在\n";
    exit(1);
}

echo "正在引入common.php...\n";
require_once $commonPath;

echo "检查DB变量...\n";
echo "DB变量状态: " . (isset($DB) ? '已定义' : '未定义') . "\n";

if (isset($DB)) {
    echo "DB对象类型: " . get_class($DB) . "\n";
    
    try {
        // 测试简单查询
        $result = $DB->query("SELECT COUNT(*) as count FROM ozon_order LIMIT 1");
        if ($result) {
            echo "数据库连接测试: 成功\n";
            $row = $DB->fetch($result);
            echo "ozon_order表记录数: " . $row['count'] . "\n";
        } else {
            echo "数据库连接测试: 查询失败\n";
        }
    } catch (Exception $e) {
        echo "数据库连接测试: 异常 - " . $e->getMessage() . "\n";
    }
} else {
    echo "错误: DB对象未初始化\n";
    
    // 检查配置文件
    $configPath = __DIR__ . '/../config.php';
    echo "Config.php路径: {$configPath}\n";
    echo "Config.php是否存在: " . (file_exists($configPath) ? '是' : '否') . "\n";
    
    if (file_exists($configPath)) {
        echo "显示配置文件内容...\n";
        $configContent = file_get_contents($configPath);
        $lines = explode("\n", $configContent);
        foreach ($lines as $i => $line) {
            if ($i < 20) { // 只显示前20行
                echo "  " . ($i + 1) . ": " . $line . "\n";
            }
        }
    }
}

echo "\n=== 测试完成 ===\n";