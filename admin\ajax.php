<?php
    include("../includes/common.php");

    // 声明这是管理员后台
    define('IN_ADMIN', true);

    $act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;

    header("Content-Type: application/json; charset=UTF-8");

    // 管理员UID列表
    $admin_uids = [10, 10000];
    
    // 极验验证配置
    $geetest_config = [
        'captcha_id' => '647f5ed2ed8acb4be36784e01556bb71', // 极验ID，需要替换为真实的
        'captcha_key' => 'b09a1b72c3d6c3a3c1f8d2e1d7e8c5a6'  // 极验Key，需要替换为真实的
    ];

    /**
     * 获取客户端真实IP地址
     * @return string
     */
    function getClientIP() {
        $ip = '';
        if (isset($_SERVER['HTTP_X_FORWARDED_FOR']) && !empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            $ip = trim($ips[0]);
        } elseif (isset($_SERVER['HTTP_X_REAL_IP']) && !empty($_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && !empty($_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif (isset($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        
        // 验证IP格式
        if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return $ip;
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }

    /**
     * 验证极验验证码
     * @param string $captcha_result 前端传来的验证结果JSON字符串
     * @return bool 验证是否通过
     */
    function validateGeetestCaptcha($captcha_result) {
        global $geetest_config;
        
        // 开发/测试模式：如果极验ID是默认的测试ID，则直接返回true
        if ($geetest_config['captcha_id'] === '647f5ed2ed8acb4be36784e01556bb71') {
            // 检查前端是否传来了验证结果（简单验证前端确实执行了验证码逻辑）
            $result_data = json_decode($captcha_result, true);
            if ($result_data && !empty($result_data)) {
                return true; // 开发模式下直接通过
            }
            return false;
        }
        
        try {
            $result_data = json_decode($captcha_result, true);
            if (!$result_data) {
                return false;
            }
            
            // 极验4.0验证参数
            $lot_number = $result_data['lot_number'] ?? '';
            $captcha_output = $result_data['captcha_output'] ?? '';
            $pass_token = $result_data['pass_token'] ?? '';
            $gen_time = $result_data['gen_time'] ?? '';
            
            if (empty($lot_number) || empty($captcha_output) || empty($pass_token) || empty($gen_time)) {
                return false;
            }
            
            // 构造验证请求参数
            $sign_token = hash_hmac('sha256', $lot_number, $geetest_config['captcha_key']);
            
            $post_data = [
                'lot_number' => $lot_number,
                'captcha_output' => $captcha_output,
                'pass_token' => $pass_token,
                'gen_time' => $gen_time,
                'sign_token' => $sign_token,
                'captcha_id' => $geetest_config['captcha_id']
            ];
            
            // 发送验证请求到极验服务器
            $url = 'https://gcaptcha4.geetest.com/validate';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post_data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code != 200 || !$response) {
                return false;
            }
            
            $response_data = json_decode($response, true);
            
            // 验证结果
            if ($response_data && isset($response_data['result']) && $response_data['result'] === 'success') {
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log('极验验证码验证异常: ' . $e->getMessage());
            return false;
        }
    }

    // 除了登录接口，其他接口都需要管理员权限验证
    if ($act != 'login' && !isset($_SESSION['admin_islogin'])) {
        exit(json_encode(['code' => -3, 'msg' => '管理员未登录']));
    }

    switch ($act) {
        case 'login':
            $username = trim($_POST['username']);
            $password = trim($_POST['password']);
            $captcha_result = isset($_POST['captcha_result']) ? $_POST['captcha_result'] : '';
            
            if (empty($username) || empty($password)) {
                exit(json_encode(['code' => -1, 'msg' => '请确保各项不能为空']));
            }
            
            // 验证滑块验证码（允许在服务不可用时跳过）
            $captcha_bypass = false;
            if (!empty($captcha_result)) {
                // 有验证码结果时进行验证
                if (!validateGeetestCaptcha($captcha_result)) {
                    exit(json_encode(['code' => -1, 'msg' => '验证码验证失败，请重试']));
                }
            } else {
                // 没有验证码结果时，记录日志但允许继续
                error_log('管理员登录：验证码服务不可用，用户: ' . $username . '，IP: ' . getClientIP());
                $captcha_bypass = true;
            }
            
            // 从ozon_user表验证用户名和密码
            $userrow = $DB->getRow("SELECT * FROM ozon_user WHERE username=:username limit 1", [':username' => $username]);
            
            if ($userrow && password_verify($password, $userrow['password'])) {
                // 验证是否为管理员UID
                if (in_array($userrow['uid'], $admin_uids)) {
                    $_SESSION['admin_islogin'] = true;
                    $_SESSION['admin_uid'] = $userrow['uid'];
                    $_SESSION['admin_username'] = $userrow['username'];
                    
                    // 如果跳过了验证码，记录日志
                    if ($captcha_bypass) {
                        error_log('管理员登录成功（验证码跳过）：用户: ' . $username . '，UID: ' . $userrow['uid'] . '，IP: ' . getClientIP());
                    }
                    
                    exit(json_encode(['code' => 1, 'msg' => '登录成功']));
                } else {
                    exit(json_encode(['code' => -1, 'msg' => '非管理员账号，禁止登录']));
                }
            } else {
                exit(json_encode(['code' => -1, 'msg' => '用户名或密码不正确']));
            }
            break;

        case 'logout':
            unset($_SESSION['admin_islogin']);
            unset($_SESSION['admin_uid']);
            unset($_SESSION['admin_username']);
            exit(json_encode(['code' => 1, 'msg' => '注销成功']));
            break;

case 'config':
    $config = 
    [
      "logo"=> [
        "title"=> "海豚 OZON 数据",
        "image"=> "../assets/admin/images/logo.png"
      ],
      "menu"=> [
        "data"=> "./ajax.php?act=menu",
        "method"=> "GET",
        "accordion"=> true,
        "collapse"=> false,
        "control"=> false,
        "controlWidth"=> 500,
        "select"=> "1",
        "async"=> true
      ],
      "tab"=> [
        "enable"=> false,
        "keepState"=> true,
        "session"=> true,
        "preload"=> false,
        "max"=> "30",
        "index"=> [
          "id"=> "1",
          "href"=> "./view/console/index.html",
          "title"=> "首页"
        ]
      ],
      "theme"=> [
        "defaultColor"=> "2",
        "defaultMenu"=> "light-theme",
        "defaultHeader"=> "light-theme",
        "allowCustom"=> true,
        "banner"=> false
      ],
      "colors"=> [
        [
          "id"=> "1",
          "color"=> "#2d8cf0",
          "second"=> "#ecf5ff"
        ],
        [
          "id"=> "2",
          "color"=> "#1677ff",
          "second"=> "#e6f4ff"
        ],
        [
          "id"=> "3",
          "color"=> "#0052d9",
          "second"=> "#d6e4ff"
        ],
        [
          "id"=> "4",
          "color"=> "#69b1ff",
          "second"=> "#f0f6ff"
        ],
        [
          "id"=> "5",
          "color"=> "#1d39c4",
          "second"=> "#cfd3f9"
        ]
      ],
      "other"=> [
        "keepLoad"=> "500",
        "autoHead"=> false,
        "footer"=> false
      ],
      "header"=> [
        "message"=> "../assets/admin/data/message.json"
      ]
    ];
    //echo file_get_contents('../assets/config/pear.config.json');exit;
    exit(json_encode($config));
break;
case 'menu':
    $menu = [
        [
            "id" => "1",
            "title" => "仪表盘",
            "icon" => "layui-icon-console",
            "type" => "1",
            "href" => "view/console/index.html"
        ],
        [
            "id" => "100",
            "title" => "用户管理",
            "icon" => "layui-icon-user",
            "type" => "1",
            "href" => "view/user/list.html",
            "children" => [
                [
                    "id" => "101",
                    "title" => "用户列表",
                    "icon" => "layui-icon-username",
                    "type" => "0",
                    "href" => "view/user/list.html"
                ]
            ]
        ],
               
                 [
                    "id" => "150",
                    "title" => "激活码管理",
                    "icon" => "layui-icon-vercode",
                    "type" => "1",
                    "href" => "view/activation/list.html"
                ],
        [
            "id" => "200",
            "title" => "店铺管理",
            "icon" => "layui-icon-home",
            "type" => "1",
            "href" => "view/store/list.html",
            "children" => [
                [
                    "id" => "201",
                    "title" => "店铺列表",
                    "icon" => "layui-icon-list",
                    "type" => "0",
                    "href" => "view/store/list.html"
                ]
            ]
        ],
        [
            "id" => "300",
            "title" => "商品管理",
            "icon" => "layui-icon-component",
            "type" => "1",
            "href" => "view/product/list.html",
            "children" => [
                [
                    "id" => "301",
                    "title" => "商品列表",
                    "icon" => "layui-icon-template-1",
                    "type" => "0",
                    "href" => "view/product/list.html"
                ]
            ]
        ],
        [
            "id" => "400",
            "title" => "订单管理",
            "icon" => "layui-icon-form",
            "type" => "1",
            "href" => "view/order/list.html",
            "children" => [
                [
                    "id" => "401",
                    "title" => "订单列表",
                    "icon" => "layui-icon-template",
                    "type" => "0",
                    "href" => "view/order/list.html"
                ]
            ]
        ],
        [
            "id" => "500",
            "title" => "系统监控",
            "icon" => "layui-icon-engine",
            "type" => "1",
            "href" => "view/monitor/network.html",
            "children" => [
                [
                    "id" => "501",
                    "title" => "网络监控",
                    "icon" => "layui-icon-website",
                    "type" => "0",
                    "href" => "view/monitor/network.html"
                ],
                [
                    "id" => "502",
                    "title" => "任务队列",
                    "icon" => "layui-icon-carousel",
                    "type" => "0",
                    "href" => "view/monitor/queue.html"
                ],
                [
                    "id" => "503",
                    "title" => "系统日志",
                    "icon" => "layui-icon-file",
                    "type" => "0",
                    "href" => "view/monitor/logs.html"
                ]
            ]
        ],
        [
            "id" => "600",
            "title" => "系统设置",
            "icon" => "layui-icon-set",
            "type" => "1",
            "href" => "view/system/notice.html",
            "children" => [
                [
                    "id" => "601",
                    "title" => "系统公告",
                    "icon" => "layui-icon-notice",
                    "type" => "0",
                    "href" => "view/system/notice.html"
                ]
            ]
        ]
    ];
    exit(json_encode($menu));
    break;

// 仪表盘数据

        // 仪表盘数据
        case 'dashboard_stats':
            try {
                // 统计数据
                $total_users = $DB->getColumn("SELECT COUNT(*) FROM ozon_user WHERE status=1");
                $today_users = $DB->getColumn("SELECT COUNT(*) FROM ozon_user WHERE DATE(addtime) = CURDATE()");
                $total_stores = $DB->getColumn("SELECT COUNT(*) FROM ozon_store");
                $total_products = $DB->getColumn("SELECT COUNT(*) FROM ozon_products");
                $total_orders = $DB->getColumn("SELECT COUNT(*) FROM ozon_orders");
                $today_orders = $DB->getColumn("SELECT COUNT(*) FROM ozon_orders WHERE DATE(created_at) = CURDATE()");
                $gmnum = $DB->getColumn("SELECT COUNT(*) FROM ozon_cron WHERE status NOT IN ('ok','no') AND date='2025-08-22'");
                $gmnumok = $DB->getColumn("SELECT COUNT(*) FROM ozon_cron WHERE status='ok' AND date='2025-08-22'");
                $gmnumno = $DB->getColumn("SELECT COUNT(*) FROM ozon_cron WHERE status='no' AND date='2025-08-22'");
                $stats = [
                    'total_users' => $total_users ?: 0,
                    'today_users' => $today_users ?: 0,
                    'total_stores' => $total_stores ?: 0,
                    'total_products' => $total_products ?: 0,
                    'total_orders' => $total_orders ?: 0,
                    'today_orders' => $today_orders ?: 0,
                    'gmnum'        => $gmnum,
                    'gmnumok'        => $gmnumok,
                    'gmnumno'        => $gmnumno
                ];
                
                exit(json_encode(['code' => 1, 'data' => $stats]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '获取统计数据失败: ' . $e->getMessage()]));
            }
            break;

        // 用户管理
        case 'user_list':
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            
            $offset = ($page - 1) * $limit;
            
            $where = "1=1";
            $params = [];
            
            if (!empty($search)) {
                $where .= " AND (username LIKE ? OR uid = ?)";
                $params[] = "%{$search}%";
                $params[] = $search;
            }
            
            // 获取总数
            $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_user WHERE {$where}", $params);
            
            // 获取用户列表，包含实际店铺数量
            $users = $DB->getAll("SELECT u.uid, u.username, u.email, u.phone, u.user_level, u.agent_level, u.max_shops, u.addtime, u.expiry_date, u.status, 
                                    COALESCE(s.shop_count, 0) as shop_count 
                                    FROM ozon_user u 
                                    LEFT JOIN (SELECT uid, COUNT(*) as shop_count FROM ozon_store GROUP BY uid) s ON u.uid = s.uid 
                                    WHERE {$where} ORDER BY u.uid DESC LIMIT {$limit} OFFSET {$offset}", $params);
            
            exit(json_encode([
                'code' => 0,
                'msg' => 'success',
                'count' => $total,
                'data' => $users ?: []
            ]));
            break;

        case 'user_edit':
            $uid = intval($_POST['uid']);
            $email = trim($_POST['email']);
            $phone = trim($_POST['phone']);
            $user_level = intval($_POST['user_level']);
            $agent_level = intval($_POST['agent_level']);
            $max_shops = intval($_POST['max_shops']);
            $expiry_date = trim($_POST['expiry_date']);
            $status = intval($_POST['status']);
            
            if ($uid <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '用户ID无效']));
            }
            
            $result = $DB->exec("UPDATE ozon_user SET email=?, phone=?, user_level=?, agent_level=?, max_shops=?, expiry_date=?, status=? WHERE uid=?", 
                            [$email, $phone, $user_level, $agent_level, $max_shops, $expiry_date, $status, $uid]);
            
            if ($result) {
                exit(json_encode(['code' => 1, 'msg' => '更新成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '更新失败']));
            }
            break;

        // 店铺管理
        case 'store_list':
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
            
            // 获取各种筛选条件
            $storename = isset($_GET['storename']) ? trim($_GET['storename']) : '';
            $username = isset($_GET['username']) ? trim($_GET['username']) : '';
            $uid = isset($_GET['uid']) ? trim($_GET['uid']) : '';
            $phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';
            $status = isset($_GET['status']) ? trim($_GET['status']) : '';
            
            // 兼容旧的搜索参数
            $search = isset($_GET['search']) ? trim($_GET['search']) : '';
            
            $offset = ($page - 1) * $limit;
            
            $where = "1=1";
            $params = [];
            
            // 新的筛选条件
            if (!empty($storename)) {
                $where .= " AND s.storename LIKE ?";
                $params[] = "%{$storename}%";
            }
            
            if (!empty($username)) {
                $where .= " AND u.username LIKE ?";
                $params[] = "%{$username}%";
            }
            
            if (!empty($uid)) {
                $where .= " AND s.uid = ?";
                $params[] = $uid;
            }
            
            if (!empty($phone)) {
                $where .= " AND u.phone LIKE ?";
                $params[] = "%{$phone}%";
            }
            
            if ($status !== '') {
                $where .= " AND s.apistatus = ?";
                $params[] = $status;
            }
            
            // 兼容旧的搜索功能
            if (!empty($search)) {
                $where .= " AND (s.storename LIKE ? OR u.username LIKE ?)";
                $params[] = "%{$search}%";
                $params[] = "%{$search}%";
            }
            
            // 获取总数
            $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_store s LEFT JOIN ozon_user u ON s.uid = u.uid WHERE {$where}", $params);
            
            // 获取店铺列表，包含用户的手机号等信息
            $stores = $DB->getAll("SELECT s.*, u.username, u.phone FROM ozon_store s LEFT JOIN ozon_user u ON s.uid = u.uid WHERE {$where} ORDER BY s.id DESC LIMIT {$limit} OFFSET {$offset}", $params);
            
            exit(json_encode([
                'code' => 0,
                'msg' => 'success',
                'count' => $total,
                'data' => $stores ?: []
            ]));
            break;

        // 编辑店铺
        case 'store_edit':
            $id = intval($_POST['id']);
            $storename = trim($_POST['storename']);
            $ClientId = intval($_POST['ClientId']);
            $key = trim($_POST['key']);
            $currency_code = trim($_POST['currency_code']);
            $apistatus = intval($_POST['apistatus']);
            
            if ($id <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '店铺ID无效']));
            }
            
            if (empty($storename)) {
                exit(json_encode(['code' => -1, 'msg' => '店铺名称不能为空']));
            }
            
            if (empty($ClientId)) {
                exit(json_encode(['code' => -1, 'msg' => 'Client ID不能为空']));
            }
            
            if (empty($key)) {
                exit(json_encode(['code' => -1, 'msg' => 'API Key不能为空']));
            }
            
            if (empty($currency_code)) {
                exit(json_encode(['code' => -1, 'msg' => '货币不能为空']));
            }
            
            try {
                // 检查店铺是否存在
                $store = $DB->getRow("SELECT id FROM ozon_store WHERE id = ?", [$id]);
                if (!$store) {
                    exit(json_encode(['code' => -1, 'msg' => '店铺不存在']));
                }
                
                // 检查Client ID是否被其他店铺使用
                $existingStore = $DB->getRow("SELECT id FROM ozon_store WHERE ClientId = ? AND id != ?", [$ClientId, $id]);
                if ($existingStore) {
                    exit(json_encode(['code' => -1, 'msg' => '该Client ID已被其他店铺使用']));
                }
                
                // 更新店铺信息
                $updateData = [
                    'storename' => $storename,
                    'ClientId' => $ClientId,
                    'key' => $key,
                    'currency_code' => $currency_code,
                    'apistatus' => $apistatus
                ];
                
                $result = $DB->update('ozon_store', $updateData, ['id' => $id]);
                
                if ($result) {
                    exit(json_encode(['code' => 0, 'msg' => '编辑成功']));
                } else {
                    exit(json_encode(['code' => -1, 'msg' => '编辑失败，没有变化或数据库错误']));
                }
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '编辑失败: ' . $e->getMessage()]));
            }
            break;

        // 删除店铺
        case 'store_delete':
            $id = intval($_POST['id']);
            
            if ($id <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '店铺ID无效']));
            }
            
            try {
                // 检查店铺是否存在
                $store = $DB->getRow("SELECT id, storename FROM ozon_store WHERE id = ?", [$id]);
                if (!$store) {
                    exit(json_encode(['code' => -1, 'msg' => '店铺不存在']));
                }
                
                // 检查是否有相关的商品数据
                $productCount = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE storeid = ?", [$id]);
                if ($productCount > 0) {
                    exit(json_encode(['code' => -1, 'msg' => '该店铺下还有 ' . $productCount . ' 个商品，请先删除商品后再删除店铺']));
                }
                
                // 检查是否有相关的订单数据
                $orderCount = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE storeid = ?", [$id]);
                if ($orderCount > 0) {
                    exit(json_encode(['code' => -1, 'msg' => '该店铺下还有 ' . $orderCount . ' 个订单，无法删除']));
                }
                
                // 删除店铺
                $result = $DB->exec("DELETE FROM ozon_store WHERE id = ?", [$id]);
                
                if ($result) {
                    exit(json_encode(['code' => 0, 'msg' => '删除成功']));
                } else {
                    exit(json_encode(['code' => -1, 'msg' => '删除失败']));
                }
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '删除失败: ' . $e->getMessage()]));
            }
            break;

        // 商品管理
        case 'product_list':
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
            
            // 获取筛选参数
            $status = isset($_GET['status']) ? trim($_GET['status']) : '';
            $text = isset($_GET['text']) ? trim($_GET['text']) : '';
            $price_min = isset($_GET['price_min']) ? trim($_GET['price_min']) : '';
            $price_max = isset($_GET['price_max']) ? trim($_GET['price_max']) : '';
            $stock_min = isset($_GET['stock_min']) ? trim($_GET['stock_min']) : '';
            $stock_max = isset($_GET['stock_max']) ? trim($_GET['stock_max']) : '';
            $storeid = isset($_GET['storeid']) ? trim($_GET['storeid']) : '';
            $uid = isset($_GET['uid']) ? trim($_GET['uid']) : '';
            $date1 = isset($_GET['date1']) ? trim($_GET['date1']) : '';
            $date2 = isset($_GET['date2']) ? trim($_GET['date2']) : '';
            
            $offset = ($page - 1) * $limit;
            
            $where = "1=1 AND p.uid NOT IN (10, 10000)";
            $params = [];
            
            // 状态筛选
            if (!empty($status) && $status !== 'all') {
                if ($status == 'published') {
                    $where .= " AND p.status_info LIKE '%Продается%'";
                } else if ($status == 'archived') {
                    $where .= " AND (p.is_archived=1 OR p.is_autoarchived=1)";
                } else if ($status == 'moderating') {
                    $where .= " AND p.status_description LIKE '%На модерации%'";
                } else if ($status == 'removed') {
                    $where .= " AND p.status_description LIKE '%Убран из продажи%'";
                } else if ($status == 'low_stock') {
                    $where .= " AND p.status_info LIKE '%Готов к продаже%'";
                } else if ($status == 'processing') {
                    $where .= " AND p.status_info NOT LIKE '%Продается%' AND (p.is_archived!=1 AND p.is_autoarchived!=1) AND p.status_description NOT LIKE '%На модерации%' AND p.status_description NOT LIKE '%Убран из продажи%' AND p.status_info NOT LIKE '%Готов к продаже%'";
                }
            }
            
            // 文本搜索
            if (!empty($text)) {
                $where .= " AND (p.name LIKE ? OR p.offer_id LIKE ? OR p.sku LIKE ? OR u.username LIKE ?)";
                $likeText = "%{$text}%";
                $params = array_merge($params, [$likeText, $likeText, $likeText, $likeText]);
            }
            
            // 价格区间筛选
            if (!empty($price_min) || !empty($price_max)) {
                if (empty($price_min)) $price_min = 0;
                if (empty($price_max)) $price_max = 999999999;
                $where .= " AND p.price >= ? AND p.price <= ?";
                $params[] = $price_min;
                $params[] = $price_max;
            }
            
            // 库存区间筛选 (这里简化处理，实际应解析stocks字段)
            if (!empty($stock_min) || !empty($stock_max)) {
                // 这里需要根据实际的stocks字段结构进行调整
                // 暂时跳过复杂的JSON解析
            }
            
            // 店铺筛选
            if (!empty($storeid)) {
                $where .= " AND p.storeid = ?";
                $params[] = $storeid;
            }
            
            // 用户筛选
            if (!empty($uid)) {
                $where .= " AND p.uid = ?";
                $params[] = $uid;
            }
            
            // 日期筛选
            if (!empty($date1) && !empty($date2)) {
                $where .= " AND DATE(p.created_at) >= ? AND DATE(p.created_at) <= ?";
                $params[] = $date1;
                $params[] = $date2;
            }
            
            try {
                // 获取总数
                $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_products p LEFT JOIN ozon_user u ON p.uid = u.uid WHERE {$where}", $params);
                
                // 获取商品列表，使用与用户端相同的字段
                $products = $DB->getAll("SELECT p.*, u.username, s.storename FROM ozon_products p 
                                        LEFT JOIN ozon_user u ON p.uid = u.uid 
                                        LEFT JOIN ozon_store s ON p.storeid = s.id 
                                        WHERE {$where} ORDER BY p.created_at DESC LIMIT {$limit} OFFSET {$offset}", $params);
                
                exit(json_encode([
                    'code' => 0,
                    'msg' => 'success',
                    'count' => $total,
                    'data' => $products ?: []
                ]));
            } catch (Exception $e) {
                exit(json_encode([
                    'code' => -1,
                    'msg' => '查询失败: ' . $e->getMessage(),
                    'count' => 0,
                    'data' => []
                ]));
            }
            break;

        // 获取商品状态统计
        case 'product_status_counts':
            try {
                $counts = [];
                
                // 全部商品
                $counts['all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000)");
                
                // 正常销售
                $counts['published'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND status_info LIKE '%Продается%'");
                
                // 已归档
                $counts['archived'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND (is_archived=1 OR is_autoarchived=1)");
                
                // 审核中
                $counts['moderating'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND status_description LIKE '%На модерации%'");
                
                // 已下架
                $counts['removed'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND status_description LIKE '%Убран из продажи%'");
                
                // 库存不足
                $counts['low_stock'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND status_info LIKE '%Готов к продаже%'");
                
                // 处理中（其他状态）
                $counts['processing'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_products WHERE uid NOT IN (10, 10000) AND status_info NOT LIKE '%Продается%' AND (is_archived!=1 AND is_autoarchived!=1) AND status_description NOT LIKE '%На модерации%' AND status_description NOT LIKE '%Убран из продажи%' AND status_info NOT LIKE '%Готов к продаже%'");
                
                exit(json_encode(['code' => 0, 'data' => $counts]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '获取统计失败: ' . $e->getMessage()]));
            }
            break;

        // 同步单个商品
        case 'sync_product':
            $id = intval($_POST['id']);
            
            if ($id <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '商品ID无效']));
            }
            
            // 这里应该调用同步商品的逻辑
            // 暂时返回成功
            exit(json_encode(['code' => 0, 'msg' => '同步成功']));
            break;

        // 批量删除商品
        case 'batch_delete_products':
            $ids_str = $_POST['ids'] ?? '';
            
            if (empty($ids_str)) {
                exit(json_encode(['code' => -1, 'msg' => '请选择要删除的商品']));
            }
            
            $ids = explode(',', $ids_str);
            $valid_ids = [];
            foreach ($ids as $id) {
                $id = intval(trim($id));
                if ($id > 0) {
                    $valid_ids[] = $id;
                }
            }
            
            if (empty($valid_ids)) {
                exit(json_encode(['code' => -1, 'msg' => '无效的商品ID']));
            }
            
            try {
                $placeholders = str_repeat('?,', count($valid_ids) - 1) . '?';
                $result = $DB->exec("DELETE FROM ozon_products WHERE id IN ({$placeholders})", $valid_ids);
                
                exit(json_encode(['code' => 0, 'msg' => '批量删除成功', 'data' => ['count' => $result]]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '删除失败: ' . $e->getMessage()]));
            }
            break;

        case 'product_delete':
            $id = intval($_POST['id']);
            
            if ($id <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '商品ID无效']));
            }
            
            $result = $DB->exec("DELETE FROM ozon_products WHERE id=?", [$id]);
            
            if ($result) {
                exit(json_encode(['code' => 1, 'msg' => '删除成功']));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '删除失败']));
            }
            break;

        // 订单管理
        case 'order_list':
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
            
            // 获取筛选参数
            $status = isset($_GET['status']) ? trim($_GET['status']) : '';
            $text = isset($_GET['text']) ? trim($_GET['text']) : '';
            $money = isset($_GET['money']) ? trim($_GET['money']) : '';
            $moneys = isset($_GET['moneys']) ? trim($_GET['moneys']) : '';
            $storeid = isset($_GET['storeid']) ? trim($_GET['storeid']) : '';
            $date1 = isset($_GET['date1']) ? trim($_GET['date1']) : '';
            $date2 = isset($_GET['date2']) ? trim($_GET['date2']) : '';
            $uid = isset($_GET['uid']) ? trim($_GET['uid']) : '';
            
            $offset = ($page - 1) * $limit;
            
            $where = "1=1 AND o.uid NOT IN (10, 10000)";
            $params = [];
            
            // 状态筛选
            if (!empty($status) && $status !== 'all') {
                if ($status == 'pending') {
                    $where .= " AND o.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending')";
                } else if ($status == 'not_purchased') {
                    $where .= " AND (o.cost IS NULL OR o.cost = '') AND o.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending', 'awaiting_verification')";
                } else if ($status == 'purchased') {
                    $where .= " AND o.cost IS NOT NULL AND o.cost != '' AND o.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending','awaiting_deliver','awaiting_registration')";
                } else if ($status == 'awaiting_deliver') {
                    $where .= " AND (manualstatus = 1 AND (o.status='awaiting_deliver' OR o.status='awaiting_registration')) AND o.packing_status=0 AND o.cost IS NOT NULL AND o.cost != ''";
                } else if ($status == 'awaiting_deliver2') {
                    $where .= " AND o.status='awaiting_deliver' AND o.packing_status=1";
                } else if ($status == 'cancelled') {
                    $where .= " AND (o.status='cancelled' OR o.status='cancelled_from_split_pending')";
                } else {
                    $where .= " AND o.status = ?";
                    $params[] = $status;
                }
            }
            
            // 文本搜索
            if (!empty($text)) {
                $where .= " AND (o.order_name LIKE ? OR o.name2 LIKE ? OR o.posting_number = ? OR o.sku = ? OR o.purchase_orderSn = ? OR o.courierNumber LIKE ? OR o.tracking_number = ? OR u.username LIKE ?)";
                $likeText = "%{$text}%";
                $params = array_merge($params, [$likeText, $likeText, $text, $text, $text, $likeText, $text, $likeText]);
            }
            
            // 店铺筛选
            if (!empty($storeid)) {
                $where .= " AND o.storeid = ?";
                $params[] = $storeid;
            }
            
            // 价格区间筛选
            if (!empty($money) || !empty($moneys)) {
                if (empty($money)) $money = 0;
                if (empty($moneys)) $moneys = 999999999;
                $where .= " AND o.price >= ? AND o.price <= ?";
                $params[] = $money;
                $params[] = $moneys;
            }
            
            // 日期筛选
            if (!empty($date1) && !empty($date2)) {
                $where .= " AND o.date >= ? AND o.date <= ?";
                $params[] = $date1;
                $params[] = $date2;
            }
            
            // 用户筛选
            if (!empty($uid)) {
                $where .= " AND o.uid = ?";
                $params[] = $uid;
            }
            
            try {
                // 获取总数
                $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_order o LEFT JOIN ozon_user u ON o.uid = u.uid WHERE {$where}", $params);
                
                // 获取订单列表，关联店铺和商品表获取更多信息
                $orders = $DB->getAll("SELECT o.*, u.username, s.storename, p.primary_image FROM ozon_order o 
                                    LEFT JOIN ozon_user u ON o.uid = u.uid 
                                    LEFT JOIN ozon_store s ON o.storeid = s.id 
                                    LEFT JOIN ozon_products p ON o.sku = p.sku AND o.storeid = p.storeid
                                    WHERE {$where} ORDER BY o.in_process_at DESC LIMIT {$limit} OFFSET {$offset}", $params);
                
                exit(json_encode([
                    'code' => 0,
                    'msg' => 'success',
                    'count' => $total,
                    'data' => $orders ?: []
                ]));
            } catch (Exception $e) {
                exit(json_encode([
                    'code' => -1,
                    'msg' => '查询失败: ' . $e->getMessage(),
                    'count' => 0,
                    'data' => []
                ]));
            }
            break;

        // 获取订单状态统计
        case 'order_status_counts':
            try {
                $counts = [];
                
                // 待处理
                $counts['pending'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending')");
                
                // 未采购
                $counts['not_purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending', 'awaiting_verification')");
                
                // 已采购
                $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND cost IS NOT NULL AND cost != '' AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver2', 'cancelled_from_split_pending','awaiting_deliver','awaiting_registration')");
                
                // 等待发货         
                $counts['awaiting_deliver'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND (status='awaiting_deliver' OR status='awaiting_registration') AND packing_status=0 AND cost IS NOT NULL AND cost != '' AND manualstatus = 1");
                
                // 交运平台
                $counts['awaiting_deliver2'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status='awaiting_deliver' AND packing_status=1");
                
                // 运输中
                $counts['delivering'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status='delivering'");
                
                // 已送达
                $counts['delivered'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status='delivered'");
                
                // 已取消
                $counts['cancelled'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND (status='cancelled' OR status='cancelled_from_split_pending')");
                
                // 未上传护照
                $counts['awaiting_verification'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status='awaiting_verification'");
                
                // 全部订单
                $counts['all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid NOT IN (10, 10000) AND status != 'cancelled' AND status != 'cancelled_from_split_pending'");
                
                exit(json_encode(['code' => 0, 'data' => $counts]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '获取统计失败: ' . $e->getMessage()]));
            }
            break;

        // 同步单个订单
        case 'sync_order':
            $id = intval($_POST['id']);
            
            if ($id <= 0) {
                exit(json_encode(['code' => -1, 'msg' => '订单ID无效']));
            }
            
            // 这里应该调用同步订单的逻辑
            // 暂时返回成功
            exit(json_encode(['code' => 0, 'msg' => '同步成功']));
            break;

        // 批量同步订单
        case 'batch_sync_orders':
            $ids_str = $_POST['ids'] ?? '';
            
            if (empty($ids_str)) {
                exit(json_encode(['code' => -1, 'msg' => '请选择要同步的订单']));
            }
            
            $ids = explode(',', $ids_str);
            $valid_ids = [];
            foreach ($ids as $id) {
                $id = intval(trim($id));
                if ($id > 0) {
                    $valid_ids[] = $id;
                }
            }
            
            if (empty($valid_ids)) {
                exit(json_encode(['code' => -1, 'msg' => '无效的订单ID']));
            }
            
            // 这里应该调用批量同步订单的逻辑
            // 暂时返回成功
            exit(json_encode(['code' => 0, 'msg' => '批量同步成功', 'data' => ['count' => count($valid_ids)]]));
            break;

        // 获取店铺列表
        case 'getShops':
            try {
                $shops = $DB->getAll("SELECT id, storename FROM ozon_store WHERE status = 1 ORDER BY storename");
                exit(json_encode(['code' => 0, 'data' => $shops ?: []]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '获取店铺列表失败: ' . $e->getMessage()]));
            }
            break;

        // 获取用户列表
        case 'getUsers':
            try {
                $users = $DB->getAll("SELECT uid, username FROM ozon_user WHERE status = 1 ORDER BY username");
                exit(json_encode(['code' => 0, 'data' => $users ?: []]));
            } catch (Exception $e) {
                exit(json_encode(['code' => -1, 'msg' => '获取用户列表失败: ' . $e->getMessage()]));
            }
            break;

        // 系统监控 - 队列状态
        case 'queue_status':
            // 这里可以检查RabbitMQ队列状态
            // 暂时返回模拟数据
            $queues = [
                ['name' => 'import_queue', 'messages' => 0, 'consumers' => 1, 'status' => 'running'],
                ['name' => 'sync_queue', 'messages' => 0, 'consumers' => 1, 'status' => 'running']
            ];
            
            exit(json_encode(['code' => 1, 'data' => $queues]));
            break;

        // 系统日志
        case 'system_logs':
            $log_type = isset($_GET['log_type']) ? trim($_GET['log_type']) : 'error';
            $lines = isset($_GET['lines']) ? intval($_GET['lines']) : 50;
            
            $log_files = [
                'error' => '../error.log',
                'worker' => '../worker/logs/sync_task.log',
                'import' => '../import_order.log'
            ];
            
            $log_file = $log_files[$log_type] ?? $log_files['error'];
            
            if (file_exists($log_file)) {
                $content = shell_exec("tail -n {$lines} " . escapeshellarg($log_file));
                exit(json_encode(['code' => 1, 'data' => $content]));
            } else {
                exit(json_encode(['code' => -1, 'msg' => '日志文件不存在']));
            }
            break;

        // 网络连接检查
        case 'network_check':
            try {
                // 引入配置变量
                global $Raconfig;
                
                // 检查结果存储
                $results = [];
                $totalChecks = 0;
                $successChecks = 0;
                $errorChecks = 0;
                
                // 1. 检查数据库连接
                try {
                    if (isset($DB)) {
                        $dbTestResult = $DB->getRow("SELECT 1 as test");
                        if ($dbTestResult) {
                            $results['database'] = ['status' => 'success', 'message' => '连接正常'];
                            $successChecks++;
                        } else {
                            $results['database'] = ['status' => 'error', 'message' => '连接失败'];
                            $errorChecks++;
                        }
                    } else {
                        $results['database'] = ['status' => 'error', 'message' => '数据库对象未初始化'];
                        $errorChecks++;
                    }
                } catch (Exception $e) {
                    $results['database'] = ['status' => 'error', 'message' => $e->getMessage()];
                    $errorChecks++;
                }
                $totalChecks++;
                
                // 2. 检查Redis连接
                try {
                    if (class_exists('Redis')) {
                        $redis = new Redis();
                        if ($redis->connect('127.0.0.1', 6379, 5)) {
                            $results['redis'] = ['status' => 'success', 'message' => '连接正常'];
                            $redis->close();
                            $successChecks++;
                        } else {
                            $results['redis'] = ['status' => 'error', 'message' => '连接失败'];
                            $errorChecks++;
                        }
                    } else {
                        $results['redis'] = ['status' => 'error', 'message' => 'Redis扩展未安装'];
                        $errorChecks++;
                    }
                } catch (Exception $e) {
                    $results['redis'] = ['status' => 'error', 'message' => $e->getMessage()];
                    $errorChecks++;
                }
                $totalChecks++;
                
                // 3. 检查RabbitMQ连接
                try {
                    if (class_exists('PhpAmqpLib\Connection\AMQPStreamConnection') && isset($Raconfig)) {
                        $connection = new PhpAmqpLib\Connection\AMQPStreamConnection(
                            $Raconfig['host'], 
                            $Raconfig['port'], 
                            $Raconfig['user'], 
                            $Raconfig['pwd'],
                            '/',
                            false,
                            'AMQPLAIN',
                            null,
                            'en_US',
                            5.0  // 连接超时
                        );
                        
                        if ($connection->is_open()) {
                            $results['rabbitmq'] = ['status' => 'success', 'message' => '连接正常'];
                            $connection->close();
                            $successChecks++;
                        } else {
                            $results['rabbitmq'] = ['status' => 'error', 'message' => '连接失败'];
                            $errorChecks++;
                        }
                    } else {
                        $results['rabbitmq'] = ['status' => 'error', 'message' => 'RabbitMQ库未安装或配置缺失'];
                        $errorChecks++;
                    }
                } catch (Exception $e) {
                    $results['rabbitmq'] = ['status' => 'error', 'message' => $e->getMessage()];
                    $errorChecks++;
                }
                $totalChecks++;
                
                // 4. 检查外部网络连接（简化版，减少超时）
                if (function_exists('curl_init')) {
                    $external_sites = [
                        'Google' => 'https://www.google.com',
                        'Baidu' => 'https://www.baidu.com'
                    ];
                    
                    $results['external'] = [];
                    foreach ($external_sites as $name => $url) {
                        try {
                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, $url);
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
                            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
                            curl_setopt($ch, CURLOPT_USERAGENT, 'Network Check Bot');
                            curl_setopt($ch, CURLOPT_NOBODY, true); // 只获取头部信息
                            
                            $response = curl_exec($ch);
                            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                            $error = curl_error($ch);
                            curl_close($ch);
                            
                            if ($response !== false && $httpCode >= 200 && $httpCode < 500) {
                                $results['external'][$name] = ['status' => 'success', 'message' => "HTTP $httpCode"];
                                $successChecks++;
                            } else {
                                $results['external'][$name] = ['status' => 'error', 'message' => "HTTP $httpCode: $error"];
                                $errorChecks++;
                            }
                        } catch (Exception $e) {
                            $results['external'][$name] = ['status' => 'error', 'message' => $e->getMessage()];
                            $errorChecks++;
                        }
                        $totalChecks++;
                    }
                } else {
                    $results['external'] = ['status' => 'error', 'message' => 'cURL扩展未安装'];
                    $errorChecks++;
                    $totalChecks++;
                }
                
                // 5. 简化代理检查
                try {
                    if (isset($DB)) {
                        $proxyCount = $DB->getColumn("SELECT COUNT(*) FROM ozon_dailiip WHERE status = 0");
                        if ($proxyCount > 0) {
                            $results['proxy'] = ['status' => 'success', 'message' => "找到 $proxyCount 个可用代理"];
                            $successChecks++;
                        } else {
                            $results['proxy'] = ['status' => 'warning', 'message' => '未找到可用代理'];
                        }
                    } else {
                        $results['proxy'] = ['status' => 'error', 'message' => '无法检查代理状态'];
                        $errorChecks++;
                    }
                } catch (Exception $e) {
                    $results['proxy'] = ['status' => 'error', 'message' => $e->getMessage()];
                    $errorChecks++;
                }
                $totalChecks++;
                
                // 6. 检查端口连通性
                $ports = [
                    '3306' => 'MySQL',
                    '6379' => 'Redis',
                    '80' => 'HTTP'
                ];
                
                $results['ports'] = [];
                foreach ($ports as $port => $service) {
                    $connection = @fsockopen('127.0.0.1', $port, $errno, $errstr, 3);
                    if ($connection) {
                        $results['ports'][$port] = ['status' => 'success', 'message' => '端口开放'];
                        fclose($connection);
                        $successChecks++;
                    } else {
                        $results['ports'][$port] = ['status' => 'error', 'message' => $errstr ?: '连接失败'];
                        $errorChecks++;
                    }
                    $totalChecks++;
                }
                
                // 生成汇总
                $summary = [
                    'total' => $totalChecks,
                    'success' => $successChecks,
                    'error' => $errorChecks,
                    'success_rate' => $totalChecks > 0 ? round(($successChecks / $totalChecks) * 100, 1) : 0
                ];
                
                $responseData = [
                    'results' => $results,
                    'summary' => $summary,
                    'timestamp' => date('Y-m-d H:i:s')
                ];
                
                exit(json_encode(['code' => 1, 'data' => $responseData]));
                
            } catch (Exception $e) {
                error_log('网络检查异常: ' . $e->getMessage());
                exit(json_encode(['code' => -1, 'msg' => '网络检查失败: ' . $e->getMessage()]));
            }
            break;

    // 激活码管理
    case 'activation_code_list':
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $status = isset($_GET['status']) ? trim($_GET['status']) : '';
        
        $offset = ($page - 1) * $limit;
        
        $where = "1=1";
        $params = [];
        
        if (!empty($search)) {
            $where .= " AND (r.code LIKE ? OR u.username LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        if ($status !== '') {
            $where .= " AND r.status = ?";
            $params[] = $status;
        }
        
        try {
            // 获取总数
            $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_redemption_codes r LEFT JOIN ozon_user u ON r.used_by_uid = u.uid WHERE {$where}", $params);
            
            // 获取激活码列表
            $codes = $DB->getAll("SELECT r.*, u.username as used_by_username FROM ozon_redemption_codes r 
                                 LEFT JOIN ozon_user u ON r.used_by_uid = u.uid 
                                 WHERE {$where} ORDER BY r.created_at DESC LIMIT {$limit} OFFSET {$offset}", $params);
            
            exit(json_encode([
                'code' => 0,
                'msg' => 'success',
                'count' => $total,
                'data' => $codes ?: []
            ]));
        } catch (Exception $e) {
            exit(json_encode([
                'code' => -1,
                'msg' => '查询失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]));
        }
        break;

     case 'generate_activation_codes':
        $input = json_decode(file_get_contents('php://input'), true);
        $user_level = isset($input['user_level']) ? intval($input['user_level']) : 0;
        $days = isset($input['days']) ? intval($input['days']) : 0;
        $num = isset($input['num']) ? intval($input['num']) : 0;
        $agent_uid = isset($input['agent_uid']) ? intval($input['agent_uid']) : null;
        $remark = isset($input['remark']) ? trim($input['remark']) : '';
        $is_trial = isset($input['is_trial']) ? intval($input['is_trial']) : 0;
        
        if ($user_level <= 0 || $days <= 0 || $num <= 0) {
            exit(json_encode(['code' => -1, 'msg' => '参数无效']));
        }
        if ($num > 100) {
            exit(json_encode(['code' => -1, 'msg' => '单次最多生成100个']));
        }
        // 体验卡校验：如果标记为体验卡，则必须为1天
        if ($is_trial === 1 && $days !== 1) {
            exit(json_encode(['code' => -1, 'msg' => '体验卡仅支持1天有效期']));
        }

        // 如果指定了代理uid，验证代理是否存在
        if ($agent_uid) {
            $agent = $DB->getRow("SELECT uid, username FROM ozon_user WHERE uid = ?", [$agent_uid]);
            if (!$agent) {
                exit(json_encode(['code' => -1, 'msg' => '指定的代理用户不存在']));
            }
        }

        try {
            $codes = [];
            // 检查是否存在 is_trial 字段（向后兼容）
            $has_is_trial_column = false;
            try {
                $col = $DB->getRow("SHOW COLUMNS FROM ozon_redemption_codes LIKE 'is_trial'");
                if ($col) { $has_is_trial_column = true; }
            } catch (Exception $e) {}
            for ($i = 0; $i < $num; $i++) {
                // 生成唯一激活码
                do {
                    $code = 'CDK' . strtoupper(bin2hex(random_bytes(8)));
                    $existing = $DB->getRow("SELECT id FROM ozon_redemption_codes WHERE code = ?", [$code]);
                } while ($existing);
                
                // 构建插入字段和值
                $fields = ['code', 'user_level', 'days', 'status', 'created_at'];
                $values = [$code, $user_level, $days, 0, date('Y-m-d H:i:s')];
                $placeholders = ['?', '?', '?', '?', '?'];

                // 是否体验卡
                if ($is_trial === 1 && $has_is_trial_column) {
                    $fields[] = 'is_trial';
                    $values[] = 1;
                    $placeholders[] = '?';
                }
                
                if ($agent_uid) {
                    $fields[] = 'agent_uid';
                    $values[] = $agent_uid;
                    $placeholders[] = '?';
                }
                
                if (!empty($remark)) {
                    $fields[] = 'remark';
                    $values[] = $remark;
                    $placeholders[] = '?';
                }
                
                $sql = "INSERT INTO ozon_redemption_codes (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
                $res = $DB->exec($sql, $values);
                
                if ($res) {
                    $codes[] = $code;
                }
            }

            exit(json_encode([
                'code' => 1,
                'msg' => "成功生成 " . count($codes) . " 个激活码",
                'data' => $codes
            ]));

        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '生成失败: ' . $e->getMessage()]));
        }
        break;

    case 'delete_activation_code':
        $id = intval($_POST['id']);
        
        if ($id <= 0) {
            exit(json_encode(['code' => -1, 'msg' => '激活码ID无效']));
        }
        
        // 检查激活码是否已被使用
        $code = $DB->getRow("SELECT * FROM ozon_redemption_codes WHERE id = ?", [$id]);
        if (!$code) {
            exit(json_encode(['code' => -1, 'msg' => '激活码不存在']));
        }
        
        if ($code['status'] == 1) {
            exit(json_encode(['code' => -1, 'msg' => '已使用的激活码不能删除']));
        }
        
        $result = $DB->exec("DELETE FROM ozon_redemption_codes WHERE id = ? AND status = 0", [$id]);
        
        if ($result) {
            exit(json_encode(['code' => 1, 'msg' => '删除成功']));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '删除失败']));
        }
        break;

    // 获取激活日志
    case 'activation_log_list':
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        
        $offset = ($page - 1) * $limit;
        
        $where = "1=1";
        $params = [];
        
        if (!empty($search)) {
            $where .= " AND (l.code LIKE ? OR u.username LIKE ?)";
            $params[] = "%{$search}%";
            $params[] = "%{$search}%";
        }
        
        try {
            // 获取总数
            $total = $DB->getColumn("SELECT COUNT(*) FROM ozon_activation_log l LEFT JOIN ozon_user u ON l.uid = u.uid WHERE {$where}", $params);
            
            // 获取激活日志列表
            $logs = $DB->getAll("SELECT l.*, u.username FROM ozon_activation_log l 
                                LEFT JOIN ozon_user u ON l.uid = u.uid 
                                WHERE {$where} ORDER BY l.activated_at DESC LIMIT {$limit} OFFSET {$offset}", $params);
            
            exit(json_encode([
                'code' => 0,
                'msg' => 'success',
                'count' => $total,
                'data' => $logs ?: []
            ]));
        } catch (Exception $e) {
            exit(json_encode([
                'code' => -1,
                'msg' => '查询失败: ' . $e->getMessage(),
                'count' => 0,
                'data' => []
            ]));
        }
        break;

    // 批量删除激活码
    case 'batch_delete_activation_codes':
        $ids = $_POST['ids'];
        
        if (empty($ids) || !is_array($ids)) {
            exit(json_encode(['code' => -1, 'msg' => '请选择要删除的激活码']));
        }
        
        // 验证所有ID都是有效的整数
        $valid_ids = [];
        foreach ($ids as $id) {
            $id = intval($id);
            if ($id > 0) {
                $valid_ids[] = $id;
            }
        }
        
        if (empty($valid_ids)) {
            exit(json_encode(['code' => -1, 'msg' => '无效的激活码ID']));
        }
        
        try {
            $placeholders = str_repeat('?,', count($valid_ids) - 1) . '?';
            
            // 检查要删除的激活码中是否有已使用的
            $used_codes = $DB->getAll("SELECT id, code FROM ozon_redemption_codes WHERE id IN ({$placeholders}) AND status = 1", $valid_ids);
            
            if (!empty($used_codes)) {
                $used_codes_str = implode(', ', array_column($used_codes, 'code'));
                exit(json_encode(['code' => -1, 'msg' => '以下激活码已被使用，无法删除: ' . $used_codes_str]));
            }
            
            // 删除未使用的激活码
            $result = $DB->exec("DELETE FROM ozon_redemption_codes WHERE id IN ({$placeholders}) AND status = 0", $valid_ids);
            
            exit(json_encode([
                'code' => 1,
                'msg' => '批量删除成功',
                'data' => ['count' => $result]
            ]));
            
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '删除失败: ' . $e->getMessage()]));
        }
        break;

    // 批量导出激活码
    case 'export_activation_codes':
        $ids_str = $_GET['ids'] ?? '';
        
        if (empty($ids_str)) {
            exit(json_encode(['code' => -1, 'msg' => '请选择要导出的激活码']));
        }
        
        $ids = explode(',', $ids_str);
        $valid_ids = [];
        foreach ($ids as $id) {
            $id = intval(trim($id));
            if ($id > 0) {
                $valid_ids[] = $id;
            }
        }
        
        if (empty($valid_ids)) {
            exit(json_encode(['code' => -1, 'msg' => '无效的激活码ID']));
        }
        
        try {
            $placeholders = str_repeat('?,', count($valid_ids) - 1) . '?';
            
            // 获取激活码数据
            $codes = $DB->getAll("SELECT r.id, r.code, r.user_level, r.days, r.status, r.agent_uid, 
                                        u1.username as used_by_username, r.used_at, r.created_at,
                                        u2.username as agent_username
                                 FROM ozon_redemption_codes r 
                                 LEFT JOIN ozon_user u1 ON r.used_by_uid = u1.uid
                                 LEFT JOIN ozon_user u2 ON r.agent_uid = u2.uid
                                 WHERE r.id IN ({$placeholders}) 
                                 ORDER BY r.created_at DESC", $valid_ids);
            
            if (empty($codes)) {
                exit(json_encode(['code' => -1, 'msg' => '没有找到要导出的激活码']));
            }
            
            // 设置CSV文件头
            $filename = '激活码导出_' . date('YmdHis') . '.csv';
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');
            
            // 输出BOM以支持中文
            echo "\xEF\xBB\xBF";
            
            // 输出CSV头部
            $headers = ['ID', '激活码', '会员等级', '有效天数', '状态', '代理UID', '代理用户名', '使用者', '使用时间', '创建时间'];
            echo implode(',', $headers) . "\n";
            
            // 输出数据行
            foreach ($codes as $code) {
                $level_map = [1 => '普通用户', 2 => 'VIP用户', 3 => 'SVIP用户', 4 => '企业用户'];
                $status_map = [0 => '未使用', 1 => '已使用'];
                
                $row = [
                    $code['id'],
                    $code['code'],
                    $level_map[$code['user_level']] ?? '未知',
                    $code['days'],
                    $status_map[$code['status']] ?? '未知',
                    $code['agent_uid'] ?? '',
                    $code['agent_username'] ?? '',
                    $code['used_by_username'] ?? '',
                    $code['used_at'] ?? '',
                    $code['created_at']
                ];
                
                // 处理包含逗号的字段
                $escaped_row = array_map(function($field) {
                    return '"' . str_replace('"', '""', $field) . '"';
                }, $row);
                
                echo implode(',', $escaped_row) . "\n";
            }
            
            exit();
            
        } catch (Exception $e) {
            exit(json_encode(['code' => -1, 'msg' => '导出失败: ' . $e->getMessage()]));
        }
        break;
  
    // 获取验证码配置
    case 'captcha':
        try {
            // 检查是否为测试环境
            if ($geetest_config['captcha_id'] === '647f5ed2ed8acb4be36784e01556bb71') {
                // 测试环境：返回极验4.0配置
                $response = [
                    'version' => 1,
                    'gt' => $geetest_config['captcha_id'],
                    'challenge' => '',
                    'new_captcha' => true,
                    'success' => 1
                ];
            } else {
                // 生产环境：尝试获取真实的验证码配置
                // 这里可以添加向极验服务器请求真实配置的逻辑
                // 暂时返回极验4.0配置
                $response = [
                    'version' => 1,
                    'gt' => $geetest_config['captcha_id'],
                    'challenge' => '',
                    'new_captcha' => true,
                    'success' => 1
                ];
            }
            
            header('Content-Type: application/json');
            exit(json_encode($response));
            
        } catch (Exception $e) {
            error_log('获取验证码配置失败: ' . $e->getMessage());
            exit(json_encode([
                'version' => 1,
                'gt' => '',
                'challenge' => '',
                'new_captcha' => true,
                'success' => 0,
                'error' => '验证码服务暂时不可用'
            ]));
        }
        break;



        default:
            exit(json_encode(['code' => -4, 'msg' => '未定义的操作: ' . $act]));
    }
    ?>





