<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

// 从命令行参数获取配置
$storeId = $argv[1] ?? 37;
$status = $argv[2] ?? 'delivered';
$days = $argv[3] ?? 90;

// 获取店铺数据
$row = $DB->find('store', '*', ['id' => $storeId]);
if (!$row) {
    echo "错误: 找不到ID为{$storeId}的店铺\n";
    exit(1);
}

echo "=== 分页测试脚本 ===\n";
echo "店铺ID: {$storeId}, 状态: {$status}, 天数: {$days}\n\n";

// 计算时间范围
$since = new DateTime("-{$days} days", new DateTimeZone('UTC'));
$to = new DateTime('yesterday', new DateTimeZone('UTC'));
$to->setTime(23, 59, 59);

echo "时间范围: " . $since->format('Y-m-d H:i:s') . " 到 " . $to->format('Y-m-d H:i:s') . "\n\n";

// 使用您之前成功的请求格式
function testPage($row, $status, $since, $to, $offset = 0) {
    $url = 'https://api-seller.ozon.ru/v3/posting/fbs/list';
    
    // 构建请求参数（使用您之前成功的格式）
    $requestParams = [
        "dir" => "ASC",
        "filter" => [
            "since" => $since->format('Y-m-d\TH:i:s.v\Z'),
            "to" => $to->format('Y-m-d\TH:i:s.v\Z')
        ],
        "limit" => 100,
        "offset" => $offset
    ];
    
    // 添加状态过滤
    if ($status !== 'all') {
        $requestParams["filter"]["status"] = $status;
    }
    
    $ch = curl_init();
    $headers = [
        'Client-Id: ' . $row['ClientId'],
        'Api-Key: ' . $row['key'],
        'Content-Type: application/json'
    ];
    
    $jsonData = json_encode($requestParams, JSON_UNESCAPED_UNICODE);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_POSTFIELDS => $jsonData,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    echo "=== 页面 " . (intval($offset/100) + 1) . " (offset: {$offset}) ===\n";
    echo "请求参数: " . $jsonData . "\n";
    echo "HTTP状态码: {$httpCode}\n";
    
    if ($curlError) {
        echo "cURL错误: {$curlError}\n";
        return false;
    }
    
    $data = json_decode($response, true);
    if (!$data) {
        echo "JSON解析失败\n";
        echo "原始响应: " . substr($response, 0, 500) . "\n";
        return false;
    }
    
    if (isset($data['error'])) {
        echo "API错误: " . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . "\n";
        return false;
    }
    
    if (isset($data['result']['postings'])) {
        $count = count($data['result']['postings']);
        echo "返回订单数: {$count}\n";
        
        if ($count > 0) {
            echo "第一个订单号: " . $data['result']['postings'][0]['posting_number'] . "\n";
            echo "最后一个订单号: " . $data['result']['postings'][$count-1]['posting_number'] . "\n";
        }
        
        return $count;
    } else {
        echo "响应中没有postings字段\n";
        echo "响应结构: " . json_encode(array_keys($data), JSON_UNESCAPED_UNICODE) . "\n";
        return 0;
    }
}

// 测试前3页
$totalOrders = 0;
$offset = 0;
$pageNum = 1;

while ($pageNum <= 5) {  // 最多测试5页
    $count = testPage($row, $status, $since, $to, $offset);
    
    if ($count === false) {
        echo "第{$pageNum}页请求失败，停止测试\n";
        break;
    }
    
    $totalOrders += $count;
    echo "累计订单数: {$totalOrders}\n\n";
    
    if ($count < 100) {
        echo "第{$pageNum}页返回{$count}条数据，少于100条，已获取所有数据\n";
        break;
    }
    
    // 更新offset到下一页
    $offset += 100;
    $pageNum++;
    
    // 添加延迟
    sleep(1);
}

echo "\n=== 测试完成 ===\n";
echo "总计获取: {$totalOrders} 个订单\n";
echo "测试页数: " . ($pageNum - 1) . " 页\n";