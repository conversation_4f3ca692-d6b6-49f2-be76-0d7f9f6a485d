<?php
/**
 * 订单数据调试脚本
 * 用于检查JSON文件和数据库中的订单数据匹配情况
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

echo "=== 订单数据调试工具 ===\n\n";

// 检查命令行参数
$mode = $argv[1] ?? 'help';

switch ($mode) {
    case 'help':
        showHelp();
        break;
        
    case 'check-json':
        $filename = $argv[2] ?? '';
        if (empty($filename)) {
            echo "错误: 请提供JSON文件名\n";
            echo "示例: php debug_order_data.php check-json 139_delivered_90days.json\n";
            exit(1);
        }
        checkJsonFile($filename);
        break;
        
    case 'check-db':
        $postingNumber = $argv[2] ?? '';
        if (empty($postingNumber)) {
            echo "错误: 请提供订单号\n";
            echo "示例: php debug_order_data.php check-db 0403-0001-0001\n";
            exit(1);
        }
        checkDatabaseOrder($postingNumber);
        break;
        
    case 'compare':
        $filename = $argv[2] ?? '';
        if (empty($filename)) {
            echo "错误: 请提供JSON文件名\n";
            echo "示例: php debug_order_data.php compare 139_delivered_90days.json\n";
            exit(1);
        }
        compareJsonWithDatabase($filename);
        break;
        
    case 'stats':
        showDatabaseStats();
        break;
        
    default:
        echo "错误: 未知的模式 '{$mode}'\n\n";
        showHelp();
        exit(1);
}

function showHelp() {
    echo "使用方法:\n";
    echo "  php debug_order_data.php [模式] [参数]\n\n";
    echo "可用模式:\n";
    echo "  help                          显示此帮助信息\n";
    echo "  check-json [文件名]            检查JSON文件内容\n";
    echo "  check-db [订单号]              检查数据库中的订单\n";
    echo "  compare [文件名]               对比JSON文件与数据库\n";
    echo "  stats                         显示数据库统计信息\n\n";
    echo "示例:\n";
    echo "  php debug_order_data.php check-json 139_delivered_90days.json\n";
    echo "  php debug_order_data.php check-db 0403-0001-0001\n";
    echo "  php debug_order_data.php compare 139_delivered_90days.json\n";
    echo "  php debug_order_data.php stats\n";
}

function checkJsonFile($filename) {
    global $DB;
    
    $filepath = __DIR__ . '/data/' . $filename;
    
    if (!file_exists($filepath)) {
        echo "错误: 文件不存在 - {$filename}\n";
        return;
    }
    
    echo "检查JSON文件: {$filename}\n";
    echo str_repeat('-', 50) . "\n";
    
    $content = file_get_contents($filepath);
    $data = json_decode($content, true);
    
    if ($data === null) {
        echo "错误: JSON格式无效\n";
        return;
    }
    
    echo "文件大小: " . number_format(filesize($filepath)) . " 字节\n";
    echo "订单数量: " . count($data) . "\n\n";
    
    if (!empty($data)) {
        echo "前3个订单样本:\n";
        for ($i = 0; $i < min(3, count($data)); $i++) {
            $order = $data[$i];
            echo "  订单 " . ($i + 1) . ":\n";
            echo "    posting_number: " . ($order['posting_number'] ?? '未设置') . "\n";
            echo "    status: " . ($order['status'] ?? '未设置') . "\n";
            echo "    store_id: " . ($order['store_id'] ?? '未设置') . "\n";
            echo "    created_at: " . ($order['created_at'] ?? '未设置') . "\n";
            echo "\n";
        }
        
        // 统计状态分布
        $statusCount = [];
        $storeCount = [];
        
        foreach ($data as $order) {
            $status = $order['status'] ?? '未知';
            $storeId = $order['store_id'] ?? '未知';
            
            $statusCount[$status] = ($statusCount[$status] ?? 0) + 1;
            $storeCount[$storeId] = ($storeCount[$storeId] ?? 0) + 1;
        }
        
        echo "状态分布:\n";
        foreach ($statusCount as $status => $count) {
            echo "  {$status}: {$count} 个\n";
        }
        
        echo "\n店铺分布:\n";
        foreach ($storeCount as $storeId => $count) {
            echo "  店铺 {$storeId}: {$count} 个\n";
        }
    }
}

function checkDatabaseOrder($postingNumber) {
    global $DB;
    
    echo "检查数据库中的订单: {$postingNumber}\n";
    echo str_repeat('-', 50) . "\n";
    
    // 查询订单信息
    $order = $DB->getRow("SELECT * FROM ozon_order WHERE posting_number = :posting_number LIMIT 1", 
        [':posting_number' => $postingNumber]);
    
    if (!$order) {
        echo "❌ 订单不存在\n\n";
        
        // 模糊查询相似的订单号
        echo "查找相似的订单号:\n";
        $similarOrders = $DB->getAll("SELECT posting_number, status, storeid FROM ozon_order WHERE posting_number LIKE :pattern LIMIT 5", 
            [':pattern' => '%' . substr($postingNumber, -6) . '%']);
        
        if ($similarOrders) {
            foreach ($similarOrders as $similar) {
                echo "  {$similar['posting_number']} (状态: {$similar['status']}, 店铺: {$similar['storeid']})\n";
            }
        } else {
            echo "  没有找到相似的订单号\n";
        }
        return;
    }
    
    echo "✅ 订单存在\n\n";
    echo "订单详情:\n";
    echo "  posting_number: {$order['posting_number']}\n";
    echo "  order_id: {$order['order_id']}\n";
    echo "  uid: {$order['uid']}\n";
    echo "  storeid: {$order['storeid']}\n";
    echo "  status: {$order['status']}\n";
    echo "  substatus: {$order['substatus']}\n";
    echo "  order_name: {$order['order_name']}\n";
    echo "  sku: {$order['sku']}\n";
    echo "  price: {$order['price']}\n";
    echo "  quantity: {$order['quantity']}\n";
    echo "  in_process_at: {$order['in_process_at']}\n";
    echo "  shipment_date: {$order['shipment_date']}\n";
    
    // 检查是否有products字段数据
    if (!empty($order['products'])) {
        $products = json_decode($order['products'], true);
        if ($products) {
            echo "  商品数量: " . count($products) . " 个\n";
        }
    }
}

function compareJsonWithDatabase($filename) {
    global $DB;
    
    $filepath = __DIR__ . '/data/' . $filename;
    
    if (!file_exists($filepath)) {
        echo "错误: 文件不存在 - {$filename}\n";
        return;
    }
    
    echo "对比JSON文件与数据库: {$filename}\n";
    echo str_repeat('-', 50) . "\n";
    
    $content = file_get_contents($filepath);
    $data = json_decode($content, true);
    
    if ($data === null) {
        echo "错误: JSON格式无效\n";
        return;
    }
    
    $totalOrders = count($data);
    $foundInDb = 0;
    $notFoundInDb = 0;
    $statusMatches = 0;
    $statusDiffers = 0;
    
    echo "开始对比 {$totalOrders} 个订单...\n\n";
    
    $notFoundOrders = [];
    $statusDiffOrders = [];
    
    foreach ($data as $index => $jsonOrder) {
        if (($index + 1) % 50 == 0) {
            echo "已处理: " . ($index + 1) . "/{$totalOrders}\n";
        }
        
        $postingNumber = $jsonOrder['posting_number'] ?? '';
        $jsonStatus = $jsonOrder['status'] ?? '';
        $jsonStoreId = $jsonOrder['store_id'] ?? null;
        
        if (empty($postingNumber)) {
            continue;
        }
        
        // 查询数据库
        $conditions = ['posting_number' => $postingNumber];
        if ($jsonStoreId !== null) {
            $conditions['storeid'] = $jsonStoreId;  // 使用 storeid
        }
        
        $dbOrder = $DB->find('ozon_order', ['posting_number', 'status', 'storeid'], $conditions);
        
        if (!$dbOrder) {
            $notFoundInDb++;
            $notFoundOrders[] = [
                'posting_number' => $postingNumber,
                'json_store_id' => $jsonStoreId,
                'json_status' => $jsonStatus
            ];
        } else {
            $foundInDb++;
            $dbStatus = $dbOrder['status'] ?? '';
            
            if ($dbStatus === $jsonStatus) {
                $statusMatches++;
            } else {
                $statusDiffers++;
                $statusDiffOrders[] = [
                    'posting_number' => $postingNumber,
                    'db_status' => $dbStatus,
                    'json_status' => $jsonStatus,
                    'store_id' => $dbOrder['storeid']
                ];
            }
        }
    }
    
    echo "\n对比结果:\n";
    echo "  JSON中的订单总数: {$totalOrders}\n";
    echo "  数据库中找到: {$foundInDb}\n";
    echo "  数据库中未找到: {$notFoundInDb}\n";
    echo "  状态匹配: {$statusMatches}\n";
    echo "  状态不匹配: {$statusDiffers}\n\n";
    
    if (!empty($notFoundOrders)) {
        echo "未找到的订单样本 (前10个):\n";
        for ($i = 0; $i < min(10, count($notFoundOrders)); $i++) {
            $order = $notFoundOrders[$i];
            echo "  {$order['posting_number']} (店铺: {$order['json_store_id']}, 状态: {$order['json_status']})\n";
        }
        echo "\n";
    }
    
    if (!empty($statusDiffOrders)) {
        echo "状态不匹配的订单样本 (前10个):\n";
        for ($i = 0; $i < min(10, count($statusDiffOrders)); $i++) {
            $order = $statusDiffOrders[$i];
            echo "  {$order['posting_number']}: DB({$order['db_status']}) vs JSON({$order['json_status']})\n";
        }
    }
}

function showDatabaseStats() {
    global $DB;
    
    echo "数据库统计信息\n";
    echo str_repeat('-', 50) . "\n";
    
    // 总订单数
    $totalOrders = $DB->getColumn("SELECT COUNT(*) FROM ozon_order");
    echo "总订单数: " . number_format($totalOrders) . "\n\n";
    
    // 按状态统计
    echo "按状态统计:\n";
    $statusStats = $DB->getAll("SELECT status, COUNT(*) as count FROM ozon_order GROUP BY status ORDER BY count DESC");
    foreach ($statusStats as $stat) {
        $status = $stat['status'] ?: '(空)';
        echo "  {$status}: " . number_format($stat['count']) . "\n";
    }
    echo "\n";
    
    // 按店铺统计
    echo "按店铺统计 (前10个):\n";
    $storeStats = $DB->getAll("SELECT storeid, COUNT(*) as count FROM ozon_order GROUP BY storeid ORDER BY count DESC LIMIT 10");
    foreach ($storeStats as $stat) {
        $storeId = $stat['storeid'] ?: '(空)';
        echo "  店铺 {$storeId}: " . number_format($stat['count']) . "\n";
    }
    echo "\n";
    
    // 最近的订单
    echo "最近的10个订单:\n";
    $recentOrders = $DB->getAll("SELECT posting_number, status, storeid, in_process_at FROM ozon_order ORDER BY in_process_at DESC LIMIT 10");
    foreach ($recentOrders as $order) {
        echo "  {$order['posting_number']} ({$order['status']}, 店铺: {$order['storeid']}, {$order['in_process_at']})\n";
    }
}