<?php
/**
 * Ozon 批量订单同步工具
 * 支持多种同步模式和配置
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

class OzonBatchSync {
    private $DB;
    private $debug;
    
    public function __construct($debug = false) {
        global $DB;
        $this->DB = $DB;
        $this->debug = $debug;
    }
    
    /**
     * 显示使用帮助
     */
    public function showHelp() {
        echo "=== Ozon 批量订单同步工具 ===\n\n";
        echo "使用方法:\n";
        echo "php batch_sync.php [模式] [参数]\n\n";
        
        echo "模式1 - 单个店铺:\n";
        echo "  php batch_sync.php single [店铺ID] [状态] [天数] [debug]\n";
        echo "  示例: php batch_sync.php single 37 delivered 90\n\n";
        
        echo "模式2 - 多个店铺:\n";
        echo "  php batch_sync.php multi [店铺ID列表] [状态] [天数] [debug]\n";
        echo "  示例: php batch_sync.php multi '37,38,39' delivered 90\n\n";
        
        echo "模式3 - 所有店铺:\n";
        echo "  php batch_sync.php all [状态] [天数] [debug]\n";
        echo "  示例: php batch_sync.php all delivered 90\n\n";
        
        echo "模式4 - 预设任务:\n";
        echo "  php batch_sync.php preset [任务名称]\n";
        echo "  可用任务: daily, weekly, monthly, all_status\n\n";
        
        echo "参数说明:\n";
        echo "  状态: awaiting_packaging, awaiting_deliver, delivering, delivered, all\n";
        echo "  天数: 查询的时间范围（天）\n";
        echo "  debug: 添加此参数开启调试模式\n\n";
    }
    
    /**
     * 获取所有活跃店铺
     */
    public function getActiveStores() {
        $stores = $this->DB->findAll('store', ['id', 'ClientId', 'status'], ['status' => 1]);
        if (empty($stores)) {
            $stores = $this->DB->findAll('store', ['id', 'ClientId', 'status'], []);
        }
        return $stores;
    }
    
    /**
     * 执行单个同步任务
     */
    public function runSync($storeIds, $status, $days) {
        $command = "php " . __DIR__ . "/d2_multi.php";
        $storeParam = is_array($storeIds) ? implode(',', $storeIds) : $storeIds;
        
        $args = [
            escapeshellarg($storeParam),
            escapeshellarg($status),
            escapeshellarg($days)
        ];
        
        if ($this->debug) {
            $args[] = 'debug';
        }
        
        $fullCommand = $command . ' ' . implode(' ', $args);
        
        echo "执行命令: {$fullCommand}\n";
        echo str_repeat('-', 60) . "\n";
        
        // 执行命令并实时显示输出
        $handle = popen($fullCommand . ' 2>&1', 'r');
        if ($handle) {
            while (!feof($handle)) {
                $line = fgets($handle);
                if ($line !== false) {
                    echo $line;
                }
            }
            $exitCode = pclose($handle);
            
            echo str_repeat('-', 60) . "\n";
            echo "命令执行完成，退出码: {$exitCode}\n\n";
            
            return $exitCode === 0;
        } else {
            echo "错误: 无法执行命令\n\n";
            return false;
        }
    }
    
    /**
     * 执行预设任务
     */
    public function runPresetTask($taskName) {
        $tasks = $this->getPresetTasks();
        
        if (!isset($tasks[$taskName])) {
            echo "错误: 未知的预设任务 '{$taskName}'\n\n";
            echo "可用的预设任务:\n";
            foreach ($tasks as $name => $task) {
                echo "- {$name}: {$task['description']}\n";
            }
            return false;
        }
        
        $task = $tasks[$taskName];
        echo "=== 执行预设任务: {$taskName} ===\n";
        echo "描述: {$task['description']}\n\n";
        
        $success = true;
        foreach ($task['jobs'] as $index => $job) {
            echo "[任务 " . ($index + 1) . "/" . count($task['jobs']) . "] ";
            echo "{$job['name']}\n";
            
            $result = $this->runSync($job['stores'], $job['status'], $job['days']);
            if (!$result) {
                $success = false;
                echo "警告: 任务 '{$job['name']}' 执行失败\n";
            }
            
            // 任务间延迟
            if ($index < count($task['jobs']) - 1) {
                echo "等待 2 秒后继续...\n\n";
                sleep(2);
            }
        }
        
        echo ($success ? "✅ 所有任务执行成功!" : "⚠️  部分任务执行失败") . "\n";
        return $success;
    }
    
    /**
     * 获取预设任务配置
     */
    private function getPresetTasks() {
        return [
            'daily' => [
                'description' => '每日同步任务（最近7天的所有状态订单）',
                'jobs' => [
                    ['name' => '所有店铺-待打包订单', 'stores' => 'all', 'status' => 'awaiting_packaging', 'days' => 7],
                    ['name' => '所有店铺-待发货订单', 'stores' => 'all', 'status' => 'awaiting_deliver', 'days' => 7],
                    ['name' => '所有店铺-配送中订单', 'stores' => 'all', 'status' => 'delivering', 'days' => 7],
                ]
            ],
            'weekly' => [
                'description' => '每周同步任务（最近30天的订单）',
                'jobs' => [
                    ['name' => '所有店铺-全部状态订单', 'stores' => 'all', 'status' => 'all', 'days' => 30],
                ]
            ],
            'monthly' => [
                'description' => '每月同步任务（最近90天的已交付订单）',
                'jobs' => [
                    ['name' => '所有店铺-已交付订单', 'stores' => 'all', 'status' => 'delivered', 'days' => 90],
                ]
            ],
            'all_status' => [
                'description' => '所有状态同步（最近30天）',
                'jobs' => [
                    ['name' => '待打包订单', 'stores' => 'all', 'status' => 'awaiting_packaging', 'days' => 30],
                    ['name' => '待发货订单', 'stores' => 'all', 'status' => 'awaiting_deliver', 'days' => 30],
                    ['name' => '配送中订单', 'stores' => 'all', 'status' => 'delivering', 'days' => 30],
                    ['name' => '已交付订单', 'stores' => 'all', 'status' => 'delivered', 'days' => 30],
                ]
            ]
        ];
    }
}

// 主程序
if ($argc < 2) {
    $sync = new OzonBatchSync();
    $sync->showHelp();
    exit(1);
}

$mode = $argv[1];
$debug = in_array('debug', $argv);
$sync = new OzonBatchSync($debug);

switch ($mode) {
    case 'help':
    case '--help':
    case '-h':
        $sync->showHelp();
        break;
        
    case 'single':
        $storeId = $argv[2] ?? '37';
        $status = $argv[3] ?? 'delivered';
        $days = $argv[4] ?? 90;
        
        echo "=== 单店铺同步模式 ===\n";
        $success = $sync->runSync($storeId, $status, $days);
        exit($success ? 0 : 1);
        
    case 'multi':
        $storeIds = $argv[2] ?? '';
        $status = $argv[3] ?? 'delivered';
        $days = $argv[4] ?? 90;
        
        if (empty($storeIds)) {
            echo "错误: 请提供店铺ID列表\n";
            exit(1);
        }
        
        echo "=== 多店铺同步模式 ===\n";
        $success = $sync->runSync($storeIds, $status, $days);
        exit($success ? 0 : 1);
        
    case 'all':
        $status = $argv[2] ?? 'delivered';
        $days = $argv[3] ?? 90;
        
        echo "=== 所有店铺同步模式 ===\n";
        $success = $sync->runSync('all', $status, $days);
        exit($success ? 0 : 1);
        
    case 'preset':
        $taskName = $argv[2] ?? '';
        
        if (empty($taskName)) {
            echo "错误: 请提供预设任务名称\n";
            $sync->showHelp();
            exit(1);
        }
        
        $success = $sync->runPresetTask($taskName);
        exit($success ? 0 : 1);
        
    case 'list-stores':
        echo "=== 可用店铺列表 ===\n";
        $stores = $sync->getActiveStores();
        foreach ($stores as $store) {
            echo "- ID: {$store['id']}, ClientId: {$store['ClientId']}, 状态: " . ($store['status'] ?? '未知') . "\n";
        }
        break;
        
    default:
        echo "错误: 未知的模式 '{$mode}'\n\n";
        $sync->showHelp();
        exit(1);
}