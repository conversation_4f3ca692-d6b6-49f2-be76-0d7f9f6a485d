<?php
include("../includes/common.php");
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
$act=isset($_GET['act'])?daddslashes($_GET['act']):null;



if(!checkRefererHost())exit('{"code":403}');

if($act!='login' and $act!='reg'){
    if($islogin3==1){}else exit('{"code":-3,"msg":"No Login"}');
}
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');

switch ($act) {
  case 'config':
    echo file_get_contents('./config/pear.config.json');
    exit;
break;
case 'menu':
    echo file_get_contents('./config/menu.json');exit;
break;
//订单云打印
/*case 'print_label1':
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    $postingNumbers = $data['postingNumbers'] ?? [];
    
    if (empty($postingNumbers)) {
        header('Content-Type: application/json');
        exit(json_encode(['code' => 1, 'msg' => '请选择要打印标签的订单号']));
    }

    $dateFolder = date('Ymd');
    $pdfFiles = [];
    
    foreach ($postingNumbers as $postingNumber) {
        $filePath = ROOT.'/assets/order/'.$dateFolder.'/'.$postingNumber.'.pdf';
        
        if (file_exists($filePath)) {
            $pdfFiles[] = [
                'name' => $postingNumber.'.pdf',
                'content' => base64_encode(file_get_contents($filePath))
            ];  
         // 插入打印时间记录到order_times表
            $orderId = $DB->getRow("SELECT order_id FROM ozon_order WHERE posting_number = :postingNumber LIMIT 1", [':postingNumber' => $postingNumber]);
            if ($orderId) {
                $operator = '1'; // 这里可以根据实际情况获取操作人
                $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) VALUES (:orderId, :postingNumber, 'print', NOW(), :operator)", [
                    ':orderId' => $orderId['order_id'],
                    ':postingNumber' => $postingNumber,
                    ':operator' => $operator
                ]);
            }
            
        }
    }

    if (empty($pdfFiles)) {
        header('Content-Type: application/json');
        exit(json_encode(['code' => 1, 'msg' => '未找到对应的标签文件']));
    }

    header('Content-Type: application/json');
    exit(json_encode([
        'code' => 0,
        'data' => $pdfFiles,
        'msg' => '成功获取标签文件'
    ]));
break;


*/



/*
//按照数据库的date时间目录
case 'print_label':
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    $postingNumbers = $data['postingNumbers'] ?? [];
    
    if (empty($postingNumbers)) {
        header('Content-Type: application/json');
        exit(json_encode(['code' => 1, 'msg' => '请选择要打印标签的订单号']));
    }

    $pdfFiles = [];
    $errors = [];
    
    foreach ($postingNumbers as $postingNumber) {
        // 1. 查询订单基本信息（包括date字段）
        $orderRow = $DB->getRow("SELECT order_id, storeid, `date` FROM ozon_order WHERE posting_number = :postingNumber LIMIT 1", 
            [':postingNumber' => $postingNumber]);
        
        if (!$orderRow) {
            $errors[] = "订单号 {$postingNumber} 不存在";
            continue;
        }

        // 2. 从订单数据获取日期并格式化为文件夹名称
        $dateFolder = date('Ymd', strtotime($orderRow['date']));
        
        // 3. 查询店铺密钥信息
        $storeRow = $DB->getRow("SELECT ClientId, `key` FROM ozon_store WHERE id = :storeid LIMIT 1", 
            [':storeid' => $orderRow['storeid']]);
            
        if (!$storeRow) {
            $errors[] = "订单号 {$postingNumber} 的店铺配置缺失";
            continue;
        }
        
        // 4. 构建文件路径（使用订单日期）
        $filePath = ROOT.'/assets/order/'.$dateFolder.'/'.$postingNumber.'.pdf';
        $fileDir = dirname($filePath);
        
        // 5. 检查/创建目录
        if (!is_dir($fileDir)) {
            if (!mkdir($fileDir, 0777, true) && !is_dir($fileDir)) {
                $errors[] = "创建目录失败: {$fileDir}";
                continue;
            }
        }
        
        // 6. 检查文件是否存在，不存在则生成
        if (!file_exists($filePath)) {
            // 准备API请求参数
            $apiParams = [
                'posting_number' => $postingNumber,
                'date' => $orderRow['date'], // 传入日期参数
                'storeid' => $orderRow['storeid']
            ];
            
            $client = new \lib\OzonApiClient($storeRow['ClientId'], $storeRow['key']);
            $labelUrl = $client->packagelabel($apiParams);
            
            if (!$labelUrl) {
                $errors[] = "订单号 {$postingNumber} 面单生成失败";
                continue;
            }
            
            // 下载并保存PDF
            $pdfContent = file_get_contents($labelUrl);
            if ($pdfContent === false) {
                $errors[] = "订单号 {$postingNumber} 面单下载失败";
                continue;
            }
            
            if (!file_put_contents($filePath, $pdfContent)) {
                $errors[] = "订单号 {$postingNumber} 面单保存失败";
                continue;
            }
        }
        
        // 7. 读取文件内容
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            $errors[] = "订单号 {$postingNumber} 文件读取失败";
            continue;
        }
        
        $pdfFiles[] = [
            'name' => $postingNumber.'.pdf',
            'content' => base64_encode($fileContent)
        ];
        
        // 8. 记录打印时间
        $operator = '1';
        $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) 
                  VALUES (:orderId, :postingNumber, 'print', NOW(), :operator)", [
            ':orderId' => $orderRow['order_id'],
            ':postingNumber' => $postingNumber,
            ':operator' => $operator
        ]);
    }

    // 处理结果返回
    if (empty($pdfFiles)) {
        $errorMsg = !empty($errors) ? implode('; ', $errors) : '未找到对应的标签文件';
        exit(json_encode(['code' => 1, 'msg' => $errorMsg]));
    }

    $response = [
        'code' => 0,
        'data' => $pdfFiles,
        'msg' => '成功获取标签文件'
    ];
    
    if (!empty($errors)) {
        $response['msg'] .= '（部分失败: ' . implode('; ', $errors) . '）';
    }

    header('Content-Type: application/json');
    exit(json_encode($response));
break;

*/



// 获取打印日志列表
case 'get_print_logs':
    // 验证打包员登录状态
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取打包员关联的用户ID列表
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId", [':packerId' => $packerId]);
    if (!$packerInfo) {
        exit(json_encode(['code' => 401, 'msg' => '打包员信息不存在']));
    }
    
    $allowedUids = [];
    if (!empty($packerInfo['user_uid'])) {
        $uidStr = trim($packerInfo['user_uid']);
        if (substr($uidStr, 0, 1) === '[' && substr($uidStr, -1) === ']') {
            // JSON数组格式
            $uidArray = json_decode($uidStr, true);
            if (is_array($uidArray)) {
                $allowedUids = array_map('intval', $uidArray);
            }
        } else {
            // 逗号分隔字符串格式
            $allowedUids = array_map('intval', explode(',', $uidStr));
        }
    }
    
    if (empty($allowedUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无权限访问订单数据']));
    }
    
    // 获取分页参数
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
    $offset = ($page - 1) * $limit;
    
    // 获取搜索参数
    $posting_number = isset($_GET['posting_number']) ? trim($_GET['posting_number']) : '';
    $date_start = isset($_GET['date_start']) ? trim($_GET['date_start']) : '';
    $date_end = isset($_GET['date_end']) ? trim($_GET['date_end']) : '';
    $packing_status = isset($_GET['packing_status']) ? trim($_GET['packing_status']) : '';
    
    // 构建查询条件
    $whereConditions = [];
    $params = [];
    
    // 权限过滤
    $uidCondition = implode(',', $allowedUids);
    $whereConditions[] = "o.uid IN ($uidCondition)";
    
    // 只查询打印类型的记录
    $whereConditions[] = "ot.time_type = 'print'";
    
    // 搜索条件
    if (!empty($posting_number)) {
        $whereConditions[] = "ot.posting_number LIKE :posting_number";
        $params[':posting_number'] = '%' . $posting_number . '%';
    }
    
    if (!empty($date_start)) {
        $whereConditions[] = "DATE(ot.time_value) >= :date_start";
        $params[':date_start'] = $date_start;
    }
    
    if (!empty($date_end)) {
        $whereConditions[] = "DATE(ot.time_value) <= :date_end";
        $params[':date_end'] = $date_end;
    }
    
    if (!empty($packing_status)) {
        if ($packing_status === 'null') {
            $whereConditions[] = "pr.packing_status IS NULL";
        } else {
            $whereConditions[] = "pr.packing_status = :packing_status";
            $params[':packing_status'] = $packing_status;
        }
    }
    
    $whereClause = implode(' AND ', $whereConditions);
    
    // 获取总记录数
    $countSql = "SELECT COUNT(*) FROM order_times ot 
                 LEFT JOIN ozon_order o ON ot.posting_number = o.posting_number 
                 LEFT JOIN packing_records pr ON ot.posting_number = pr.posting_number 
                 WHERE $whereClause";
    $total = $DB->getColumn($countSql, $params);
    
    // 获取数据列表，关联packing_records表获取打包状态，关联ozon_products表获取图片信息
    $sql = "SELECT ot.*, o.sku, o.quantity, o.price, o.offer_id, s.storename, pr.packing_status, p.primary_image 
            FROM order_times ot 
            LEFT JOIN ozon_order o ON ot.posting_number = o.posting_number 
            LEFT JOIN ozon_store s ON o.storeid = s.id 
            LEFT JOIN packing_records pr ON ot.posting_number = pr.posting_number 
            LEFT JOIN ozon_products p ON o.sku = p.sku AND o.storeid = p.storeid 
            WHERE $whereClause 
            ORDER BY ot.time_value DESC 
            LIMIT $limit OFFSET $offset";
    
    $list = $DB->getAll($sql, $params);
    
    // 格式化数据
    $data = [];
    foreach ($list as $row) {
        // 打包状态映射
        $packingStatusMap = [
            '0' => '异常',
            '1' => '打包完成', 
            '2' => '退回问题件'
        ];
        $packingStatus = isset($row['packing_status']) ? $packingStatusMap[$row['packing_status']] ?? '未打包' : '未打包';
        
        // 构建商品链接
        $productUrl = '';
        if (!empty($row['offer_id'])) {
            $productUrl = 'https://www.ozon.ru/product/' . $row['offer_id'] . '/';
        }
        
        $data[] = [
            'id' => $row['id'],
            'posting_number' => $row['posting_number'],
            'sku' => $row['sku'] ?: '-',
            'offer_id' => $row['offer_id'] ?: '',
            'quantity' => $row['quantity'] ?: 0,
            'price' => $row['price'] ?: '0.00',
            'storename' => $row['storename'] ?: '-',
            'print_time' => $row['time_value'],
            'operator' => $row['operator'] ?: '-',
            'packing_status' => $packingStatus,
            'packing_status_code' => $row['packing_status'] ?? null,
            'primary_image' => $row['primary_image'] ?: '../assets/img/syncing.png',
            'product_url' => $productUrl
        ];
    }
    
    exit(json_encode([
        'code' => 0,
        'msg' => 'success',
        'count' => $total,
        'data' => $data
    ]));
break;
// 获取商品图片
case 'get_product_image':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    $sku = isset($data['sku']) ? trim($data['sku']) : '';
    $storeId = isset($data['storeId']) ? intval($data['storeId']) : 0;
    
    if (!$sku || !$storeId) {
        exit(json_encode(['code' => 1, 'msg' => 'SKU和店铺ID不能为空']));
    }
    
    // 获取店铺配置
    $storeRow = $DB->getRow("SELECT ClientId, `key` FROM ozon_store WHERE id = :storeid LIMIT 1", 
        [':storeid' => $storeId]);
        
    if (!$storeRow) {
        exit(json_encode(['code' => 1, 'msg' => '店铺配置不存在']));
    }
    
    // 调用图片获取函数
    $imageData = getProductImageFromAPI($sku, $storeRow['ClientId'], $storeRow['key']);
    
    if ($imageData && isset($imageData['image'])) {
        exit(json_encode(['code' => 0, 'msg' => 'success', 'data' => $imageData]));
    } else {
        exit(json_encode(['code' => 1, 'msg' => '未找到商品图片']));
    }
break;

case 'print_label':
    // 验证打包员登录状态
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取打包员关联的用户ID列表
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId", [':packerId' => $packerId]);
    if (!$packerInfo) {
        exit(json_encode(['code' => 401, 'msg' => '打包员信息不存在']));
    }
    
    $allowedUids = [];
    if (!empty($packerInfo['user_uid'])) {
        $uidStr = trim($packerInfo['user_uid']);
        if (substr($uidStr, 0, 1) === '[' && substr($uidStr, -1) === ']') {
            // JSON数组格式
            $uidArray = json_decode($uidStr, true);
            if (is_array($uidArray)) {
                $allowedUids = array_map('intval', $uidArray);
            }
        } else {
            // 逗号分隔字符串格式
            $allowedUids = array_map('intval', explode(',', $uidStr));
        }
    }
    
    if (empty($allowedUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无权限访问订单数据']));
    }
    
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    $postingNumbers = $data['postingNumbers'] ?? [];
    
    if (empty($postingNumbers)) {
        header('Content-Type: application/json');
        exit(json_encode(['code' => 1, 'msg' => '请选择要打印标签的订单号']));
    }

    $pdfFiles = [];
    $errors = [];
    
    foreach ($postingNumbers as $postingNumber) {
        // 1. 查询订单基本信息，包含权限验证和日期信息
        $uidCondition = implode(',', $allowedUids);
        $orderRow = $DB->getRow("SELECT order_id, storeid, uid, `date` FROM ozon_order WHERE posting_number = :postingNumber AND uid IN ($uidCondition) LIMIT 1", 
            [':postingNumber' => $postingNumber]);
        
        if (!$orderRow) {
            $errors[] = "订单号 {$postingNumber} 不存在或无权限访问";
            continue;
        }
        
        // 2. 查询店铺密钥信息
        $storeRow = $DB->getRow("SELECT ClientId, `key` FROM ozon_store WHERE id = :storeid LIMIT 1", 
            [':storeid' => $orderRow['storeid']]);
            
        if (!$storeRow) {
            $errors[] = "订单号 {$postingNumber} 的店铺配置缺失";
            continue;
        }
        
        // 3. 构建文件路径 - 使用订单日期而不是当前日期
        $dateFolder = date('Ymd', strtotime($orderRow['date']));
        $filePath = ROOT.'/assets/order/'.$dateFolder.'/'.$postingNumber.'.pdf';
        $fileDir = dirname($filePath);
        
        // 4. 检查/创建目录
        if (!is_dir($fileDir)) {
            if (!mkdir($fileDir, 0777, true) && !is_dir($fileDir)) {
                $errors[] = "创建目录失败: {$fileDir}";
                continue;
            }
        }
        
        // 5. 检查文件是否存在，不存在则生成
        // 首先检查订单日期目录，如果不存在则检查当前日期目录（兼容旧文件）
        $alternativeFilePath = ROOT.'/assets/order/'.date('Ymd').'/'.$postingNumber.'.pdf';
        
        if (file_exists($filePath)) {
            // 文件存在于订单日期目录中
        } else if (file_exists($alternativeFilePath) && $dateFolder !== date('Ymd')) {
            // 文件存在于当前日期目录中，使用该文件
            $filePath = $alternativeFilePath;
        } else {
            // 文件不存在，需要生成
                        $client = new \lib\OzonApiClient($storeRow['ClientId'], $storeRow['key']);
            
            // 准备API请求参数，包含订单日期信息
            $apiParams = [
                'posting_number' => $postingNumber,
                'in_process_at' => $orderRow['date'] // 传入订单日期
            ];
            
            $labelUrl = $client->packagelabel($apiParams);
            
            if (!$labelUrl) {
                $errors[] = "订单号 {$postingNumber} 面单生成失败";
                continue;
            }
            
            // 检查返回的是否为文件路径
            if (is_string($labelUrl) && (strpos($labelUrl, '/assets/order/') === 0 || strpos($labelUrl, 'assets/order/') === 0)) {
                // 返回的是文件路径，更新为实际文件路径
                $actualFilePath = ROOT . ltrim($labelUrl, '/');
                if (file_exists($actualFilePath)) {
                    $filePath = $actualFilePath;
                } else {
                    $errors[] = "订单号 {$postingNumber} 面单文件不存在: " . $actualFilePath;
                    continue;
                }
            } else {
                $errors[] = "订单号 {$postingNumber} 面单API返回异常";
                continue;
            }
        } // 结束文件不存在的处理
        
        // 6. 读取文件内容
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            $errors[] = "订单号 {$postingNumber} 文件读取失败";
            continue;
        }
        
        $pdfFiles[] = [
            'name' => $postingNumber.'.pdf',
            'content' => base64_encode($fileContent)
        ];
        
        // 7. 记录打印时间
        $operator = '1';
        $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) 
                  VALUES (:orderId, :postingNumber, 'print', NOW(), :operator)", [
            ':orderId' => $orderRow['order_id'],
            ':postingNumber' => $postingNumber,
            ':operator' => $operator
        ]);
    }

    // 处理结果返回
    if (empty($pdfFiles)) {
        $errorMsg = !empty($errors) ? implode('; ', $errors) : '未找到对应的标签文件';
        exit(json_encode(['code' => 1, 'msg' => $errorMsg]));
    }

    $response = [
        'code' => 0,
        'data' => $pdfFiles,
        'msg' => '成功获取标签文件'
    ];
    
    if (!empty($errors)) {
        $response['msg'] .= '（部分失败: ' . implode('; ', $errors) . '）';
    }

    header('Content-Type: application/json');
    exit(json_encode($response));
break;
//获取打印机信息
case 'get_printer_config':
    // 获取当前打包员ID（根据您的登录系统实现）
    if (!$packerId) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 从数据库获取打印机配置
    $printerConfig = $DB->getRow("SELECT printer FROM packers WHERE packerId = :packerId", 
        [':packerId' => $packerId]);
    
    if (!$printerConfig || empty($printerConfig['printer'])) {
        exit(json_encode(['code' => 404, 'msg' => '未配置打印机']));
    }
    
    // 解析JSON配置
    $config = json_decode($printerConfig['printer'], true);
    if (!$config) {
        exit(json_encode(['code' => 500, 'msg' => '打印机配置格式错误']));
    }
    
    exit(json_encode([
        'code' => 0,
        'data' => $config
    ]));
break;

// 获取统计数据
case 'get_statistics':
    if ($packerId <= 0) exit(json_encode(['code' => 401, 'msg' => '未登录']));
    
    $where = "packerId = :packerId AND packing_status = 1"; // 只统计打包完成的记录
    $params = [':packerId' => $packerId];
    
    // 今日统计
    $todayWhere = $where . " AND DATE(created_at) = CURDATE()";
    $todayResult = $DB->getRow("SELECT COUNT(*) as count FROM packing_records WHERE $todayWhere", $params);
    $todayCount = $todayResult['count'] ?? 0;
    
    // 昨日统计
    $yesterdayWhere = $where . " AND DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)";
    $yesterdayResult = $DB->getRow("SELECT COUNT(*) as count FROM packing_records WHERE $yesterdayWhere", $params);
    $yesterdayCount = $yesterdayResult['count'] ?? 0;
    
    // 本周统计（周一到今天）
    $weekWhere = $where . " AND YEARWEEK(created_at, 1) = YEARWEEK(CURDATE(), 1)";
    $weekResult = $DB->getRow("SELECT COUNT(*) as count FROM packing_records WHERE $weekWhere", $params);
    $weekCount = $weekResult['count'] ?? 0;
    
    // 本月统计
    $monthWhere = $where . " AND YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())";
    $monthResult = $DB->getRow("SELECT COUNT(*) as count FROM packing_records WHERE $monthWhere", $params);
    $monthCount = $monthResult['count'] ?? 0;
    
    exit(json_encode([
        'code' => 0,
        'msg' => '',
        'data' => [
            'today' => $todayCount,
            'yesterday' => $yesterdayCount,
            'week' => $weekCount,
            'month' => $monthCount
        ]
    ]));
break;
//打包员登录
/*
case 'packer_login':
    $account = isset($_POST['account']) ? trim($_POST['account']) : '';
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';
    if (!$account || !$password) {
        exit(json_encode(['code' => 1, 'msg' => '账号和密码不能为空']));
    }
    $packer = $DB->getRow("SELECT * FROM packers WHERE account = :account LIMIT 1", [':account' => $account]);
    if (!$packer) {
        exit(json_encode(['code' => 2, 'msg' => '账号不存在']));
    }
    if (!password_verify($password, $packer['password'])) {
        exit(json_encode(['code' => 3, 'msg' => '密码错误']));
    }
     $auth = new \lib\AuthSystem();
    $token = $auth->generateToken($packer['packerId'], $account);
    
    ob_clean();
    $host = $_SERVER['HTTP_HOST'];
    $domainParts = explode('.', $host);
  // 处理本地开发环境（如localhost）
        if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
            $mainDomain = $host;
        } 
        // 处理正式域名（支持二级国家域名如.co.uk）
        else {
            $mainDomain = implode('.', array_slice($domainParts, -2, 2));
        }
      // 设置全局Cookie参数
        setcookie(
            "packerId",          // Cookie名称
            $token,                   // Token值
            time() + 691200,          // 过期时间（8天）
            "/",                      // 全路径有效
            ".{$mainDomain}",         // 支持所有子域名
            isset($_SERVER['HTTPS']), // 自动启用HTTPS安全传输
            true                      // 禁止JavaScript访问
        );	
        $result=array("code"=>0,"msg"=>"登录成功！正在跳转到用户中心");
		unset($_SESSION['csrf_token']);
     exit(json_encode(['code' => 0, 'msg' => '登录成功']));
break;
*/
case 'logout':
    setcookie("packerId", "", time() - 604800);
	@header('Content-Type: text/html; charset=UTF-8');
	$result = ['code'=>1,'您已成功注销本次登录！'];
	exit(json_encode($result));
break;
//打包员登录
case 'login':
	$username=trim($_POST['username']);
	$password=trim($_POST['password']);
	if(empty($username) || empty($password))exit('{"code":-1,"msg":"请确保各项不能为空"}');
	$userrow=$DB->getRow("SELECT * FROM packers WHERE username=:username limit 1", [':username'=>$username]);
	if($userrow && password_verify($password, $userrow['password'])) {
	    $auth = new \lib\AuthSystem();
	    $token = $auth->generateToken($userrow['packerId'], $username);
		ob_clean();
		$host = $_SERVER['HTTP_HOST'];
        $domainParts = explode('.', $host);
        
        // 处理本地开发环境（如localhost）
        if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
            $mainDomain = $host;
        } 
        // 处理正式域名（支持二级国家域名如.co.uk）
        else {
            $mainDomain = implode('.', array_slice($domainParts, -2, 2));
        }
        
        // 设置全局Cookie参数
        setcookie(
            "packerId",          // Cookie名称
            $token,                   // Token值
            time() + 691200,          // 过期时间（8天）
            "/",                      // 全路径有效
            ".{$mainDomain}",         // 支持所有子域名
            isset($_SERVER['HTTPS']), // 自动启用HTTPS安全传输
            true                      // 禁止JavaScript访问
        );
			$result=array("code"=>1,"msg"=>"登录成功！正在跳转到用户中心");
		
		unset($_SESSION['csrf_token']);
	}else {
		$result=array("code"=>-1,"msg"=>"用户名或密码不正确！");
	}
	exit(json_encode($result));
break;
//打包员注册
case 'packer_register':
    $username = isset($_POST['username']) ? trim($_POST['username']) : '';
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';
    $password_confirm = isset($_POST['password_confirm']) ? trim($_POST['password_confirm']) : '';
    if (!$username || !$password || !$password_confirm) {
        exit(json_encode(['code' => 1, 'msg' => '账号和密码不能为空']));
    }
    if ($password !== $password_confirm) {
        exit(json_encode(['code' => 2, 'msg' => '两次密码输入不一致']));
    }
    $existing = $DB->getRow("SELECT * FROM packers WHERE username = :username LIMIT 1", [':username' => $username]);
    if ($existing) {
        exit(json_encode(['code' => 3, 'msg' => '账号已存在']));
    }
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $res = $DB->exec("INSERT INTO packers (username, password, created_at) VALUES (:username, :password, NOW())", [
        ':username' => $username,
        ':password' => $hashed_password
    ]);
    if ($res === false) {
        exit(json_encode(['code' => 4, 'msg' => '注册失败']));
    }
    exit(json_encode(['code' => 0, 'msg' => '注册成功']));

break;

//扫码查询信息/查不到就自动标记异常
/*
case 'get_product_info':
    $json_data = file_get_contents('php://input');
    // 解析 JSON
    $data = json_decode(trim($json_data,':'), true);
    $courierNumber = isset($data['courierNumber']) ? trim($data['courierNumber']) : '';
    if (!$courierNumber) {
        exit(json_encode(['code' => 1, 'msg' => '快递单号不能为空']));
    }
    $products = $DB->getAll("SELECT * FROM ozon_order WHERE courierNumber=:courierNumber", [':courierNumber'=>$courierNumber]);
    if (empty($products)) {
        // 插入异常记录到packing_records表
        $insertData = [
            ':courierNumber' => $courierNumber,
            ':packing_status' => 0,
            ':created_at' => date('Y-m-d H:i:s')
        ];
        $DB->exec("INSERT INTO packing_records (tracking_number, packing_status, created_at) VALUES (:courierNumber, :packing_status, :created_at)", $insertData);
        exit(json_encode(['code' => 2, 'msg' => '未找到对应商品信息，标记查询异常']));
    }
   
    exit(json_encode(['code' => 0, 'msg' => 'success', 'data' => $products]));
break;
*/

//扫码查询信息/查不到就自动标记异常
case 'get_product_info':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    $json_data = file_get_contents('php://input');
    // 解析 JSON
    $data = json_decode(trim($json_data,':'), true);
    $courierNumber = isset($data['courierNumber']) ? trim($data['courierNumber']) : '';
    if (!$courierNumber) {
        exit(json_encode(['code' => 1, 'msg' => '快递单号不能为空']));
    }
    
    // 获取当前packerId关联的user_uid数组
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId LIMIT 1", [':packerId' => $packerId]);
    if (!$packerInfo || empty($packerInfo['user_uid'])) {
        exit(json_encode(['code' => 403, 'msg' => '无权访问该信息']));
    }
    
    // 解析user_uid - 支持JSON数组或逗号分隔的字符串格式
    $userUids = [];
    if (substr($packerInfo['user_uid'], 0, 1) === '[') {
        // JSON数组格式
        $decodedUids = json_decode($packerInfo['user_uid'], true);
        if (is_array($decodedUids)) {
            $userUids = $decodedUids;
        }
    } else {
        // 逗号分隔的字符串格式
        $userUids = explode(',', $packerInfo['user_uid']);
        // 移除可能的空白符
        $userUids = array_map('trim', $userUids);
    }
    
    if (empty($userUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无关联用户信息']));
    }
    
    // 构建IN查询的参数
    $placeholders = implode(',', array_fill(0, count($userUids), '?'));
    $params = $userUids;
    array_unshift($params, $courierNumber); // 将courierNumber添加到参数数组的开头
    
    // 只查询关联用户的订单信息，并检查是否已打印
    $products = $DB->getAll("SELECT o.*, o.OrderNotes,
                            CASE WHEN EXISTS(SELECT 1 FROM order_times WHERE posting_number = o.posting_number AND time_type = 'print') THEN 1 ELSE 0 END as is_printed,
                            (SELECT COUNT(*) FROM order_times WHERE posting_number = o.posting_number AND time_type = 'print') as print_count
                            FROM ozon_order o 
                            WHERE FIND_IN_SET(?, o.courierNumber) AND o.uid IN ($placeholders)", $params);
    
    if (empty($products)) {
        // 检查是否已经记录过该异常
        $existingRecord = $DB->getRow("SELECT id FROM packing_records WHERE tracking_number = :courierNumber AND packing_status = 0 AND packerId = :packerId AND created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)", [
            ':courierNumber' => $courierNumber,
            ':packerId' => $packerId
        ]);
        
        // 如果1天内没有记录过该异常，则记录
        if (!$existingRecord) {
            $insertData = [
                ':courierNumber' => $courierNumber,
                ':packing_status' => 0,
                ':packerId' => $packerId,
                ':created_at' => date('Y-m-d H:i:s')
            ];
            $DB->exec("INSERT INTO packing_records (tracking_number, packing_status, packerId, created_at) VALUES (:courierNumber, :packing_status, :packerId, :created_at)", $insertData);
        }
        
        // 使用特殊的错误代码告知前端这是"未找到商品"异常，避免重复提示
        exit(json_encode([
            'code' => 404,  // 使用不同的错误代码
            'msg' => '未找到对应商品信息',
            'handled' => true  // 标记已处理
        ]));
    }
    
    // 处理多个商品信息
    foreach ($products as &$product) {
        // 解析products字段中的多个商品信息
        if (!empty($product['products'])) {
            $productsData = json_decode($product['products'], true);
            if (is_array($productsData)) {
                $product['products'] = $productsData;
            }
        }
        
        // 查询是否有多个国内快递单号关联到同一个订单
        if (!empty($product['posting_number'])) {
            $courierNumbers = $DB->getAll("SELECT DISTINCT courierNumber FROM ozon_order 
                                         WHERE posting_number = :posting_number AND courierNumber != ''", 
                                         [':posting_number' => $product['posting_number']]);
            
            if ($courierNumbers && count($courierNumbers) > 0) {
                $numbers = array_column($courierNumbers, 'courierNumber');
                $product['courier_numbers'] = array_unique($numbers);
            }
            
            // 获取详细的打印历史记录
            $printHistory = $DB->getAll("SELECT time_value, operator FROM order_times 
                                       WHERE posting_number = :posting_number AND time_type = 'print' 
                                       ORDER BY time_value DESC", 
                                       [':posting_number' => $product['posting_number']]);
            
            $product['print_history'] = $printHistory;
            
            // 添加最后打印时间
            if (!empty($printHistory)) {
                $product['last_print_time'] = $printHistory[0]['time_value'];
                $product['last_print_operator'] = $printHistory[0]['operator'];
            }
        }
    }
   
    exit(json_encode(['code' => 0, 'msg' => 'success', 'data' => $products]));
break;
// 修改 mark_shipped 部分代码
// 标记发货单号
case 'mark_shipped':
    // 验证打包员登录状态
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取打包员关联的用户ID列表
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId", [':packerId' => $packerId]);
    if (!$packerInfo) {
        exit(json_encode(['code' => 401, 'msg' => '打包员信息不存在']));
    }
    
    $allowedUids = [];
    if (!empty($packerInfo['user_uid'])) {
        $uidStr = trim($packerInfo['user_uid']);
        if (substr($uidStr, 0, 1) === '[' && substr($uidStr, -1) === ']') {
            // JSON数组格式
            $uidArray = json_decode($uidStr, true);
            if (is_array($uidArray)) {
                $allowedUids = array_map('intval', $uidArray);
            }
        } else {
            // 逗号分隔字符串格式
            $allowedUids = array_map('intval', explode(',', $uidStr));
        }
    }
    
    if (empty($allowedUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无权限访问订单数据']));
    }
    
    try {
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        if ($data === null) {
            exit(json_encode(['code' => 400, 'msg' => '无效的JSON输入']));
        }
        $orders = $data['orders'] ?? [];
        $materialCost = $data['materialCost'] ?? 0.00;

        if (empty($orders)) {
            exit(json_encode(['code' => 1, 'msg' => '请选择要标记发货的订单']));
        }

        $uidCondition = implode(',', $allowedUids);
        foreach ($orders as $orderData) {
            $postingNumber = $orderData['postingNumber'] ?? '';
            $order = $DB->getRow("SELECT * FROM ozon_order WHERE posting_number = :postingNumber AND uid IN ($uidCondition) LIMIT 1", [':postingNumber' => $postingNumber]);

            if (!$order) {
                error_log("订单不存在或无权限访问: $postingNumber");
                continue;
            }

            $updateOrder = $DB->exec("UPDATE ozon_order SET packing_status = 1 WHERE posting_number = :postingNumber", [
                ':postingNumber' => $postingNumber
            ]);

            $courierNumber = $orderData['courierNumber'] ?? $order['courierNumber'] ?? 'N/A';

            $insertData = [
                ':uid' => $order['uid'],
                ':postingNumber' => $postingNumber,
                ':trackingNumber' => $order['tracking_number'] ?? 'N/A',
                ':courierNumber' => $courierNumber,
                ':packerId' => $packerId,
                ':packingQuantity' => $order['quantity'] ?? 1,
                ':materialCost' => $materialCost,
                ':packingStatus' => 1,
                ':completionTime' => date('Y-m-d H:i:s'),
                ':createdAt' => date('Y-m-d H:i:s'),
                ':updatedAt' => date('Y-m-d H:i:s')
            ];

            $insertSQL = "INSERT INTO packing_records 
            (uid, posting_number, tracking_number, courierNumber, packerId, packing_quantity, material_cost, packing_status, completion_time, created_at, updated_at) 
            VALUES 
            (:uid, :postingNumber, :trackingNumber, :courierNumber, :packerId, :packingQuantity, :materialCost, :packingStatus, :completionTime, :createdAt, :updatedAt)";

            $insertRecord = $DB->exec($insertSQL, $insertData);

            if ($insertRecord === false) {
                $errorInfo = $DB->errorInfo();
                error_log('插入失败: SQLSTATE=' . $errorInfo[0] . ', ErrorCode=' . $errorInfo[1] . ', Message=' . $errorInfo[2] . ', Data=' . json_encode($insertData));
            }
        }

        exit(json_encode(['code' => 0, 'msg' => '标记发货完成']));
    } catch (Exception $e) {
        error_log('标记发货异常：' . $e->getMessage());
        exit(json_encode(['code' => 500, 'msg' => '服务器内部错误']));
    }
break;

// 取消发货标记
case 'cancel_shipped':
    // 验证打包员登录状态
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取打包员关联的用户ID列表
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId", [':packerId' => $packerId]);
    if (!$packerInfo) {
        exit(json_encode(['code' => 401, 'msg' => '打包员信息不存在']));
    }
    
    $allowedUids = [];
    if (!empty($packerInfo['user_uid'])) {
        $uidStr = trim($packerInfo['user_uid']);
        if (substr($uidStr, 0, 1) === '[' && substr($uidStr, -1) === ']') {
            // JSON数组格式
            $uidArray = json_decode($uidStr, true);
            if (is_array($uidArray)) {
                $allowedUids = array_map('intval', $uidArray);
            }
        } else {
            // 逗号分隔字符串格式
            $allowedUids = array_map('intval', explode(',', $uidStr));
        }
    }
    
    if (empty($allowedUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无权限访问订单数据']));
    }
    
    try {
        $json_data = file_get_contents('php://input');
        $data = json_decode($json_data, true);
        if ($data === null) {
            exit(json_encode(['code' => 400, 'msg' => '无效的JSON输入']));
        }
        $postingNumbers = $data['postingNumbers'] ?? [];

        if (empty($postingNumbers)) {
            exit(json_encode(['code' => 1, 'msg' => '请选择要取消发货的订单']));
        }

        $uidCondition = implode(',', $allowedUids);
        foreach ($postingNumbers as $postingNumber) {
            // 检查订单是否存在且有权限访问
            $order = $DB->getRow("SELECT * FROM ozon_order WHERE posting_number = :postingNumber AND uid IN ($uidCondition) LIMIT 1", [':postingNumber' => $postingNumber]);

            if (!$order) {
                error_log("订单不存在或无权限访问: $postingNumber");
                continue;
            }

            // 更新订单状态为未发货
            $updateOrder = $DB->exec("UPDATE ozon_order SET packing_status = 0 WHERE posting_number = :postingNumber", [
                ':postingNumber' => $postingNumber
            ]);

            // 删除打包记录
            $deleteRecord = $DB->exec("DELETE FROM packing_records WHERE posting_number = :postingNumber", [
                ':postingNumber' => $postingNumber
            ]);

            // 记录取消发货的操作日志
            $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) 
                      VALUES (:orderId, :postingNumber, 'cancel_shipped', NOW(), :operator)", [
                ':orderId' => $order['order_id'],
                ':postingNumber' => $postingNumber,
                ':operator' => $packerId
            ]);
        }

        exit(json_encode(['code' => 0, 'msg' => '取消发货完成']));
    } catch (Exception $e) {
        error_log('取消发货异常：' . $e->getMessage());
        exit(json_encode(['code' => 500, 'msg' => '服务器内部错误']));
    }
break;

//扫码提交异常标记
//扫码提交异常标记
case 'mark_exception':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取打包员关联的用户ID列表
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId", [':packerId' => $packerId]);
    if (!$packerInfo) {
        exit(json_encode(['code' => 401, 'msg' => '打包员信息不存在']));
    }
    
    $allowedUids = [];
    if (!empty($packerInfo['user_uid'])) {
        $uidStr = trim($packerInfo['user_uid']);
        if (substr($uidStr, 0, 1) === '[' && substr($uidStr, -1) === ']') {
            // JSON数组格式
            $uidArray = json_decode($uidStr, true);
            if (is_array($uidArray)) {
                $allowedUids = array_map('intval', $uidArray);
            }
        } else {
            // 逗号分隔字符串格式
            $allowedUids = array_map('intval', explode(',', $uidStr));
        }
    }
    
    if (empty($allowedUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无权限访问订单数据']));
    }
    
    $json_data = file_get_contents('php://input');
    $data = json_decode($json_data, true);
    $trackingNumbers = isset($data['trackingNumbers']) ? $data['trackingNumbers'] : [];
    $exceptionType = isset($data['exceptionType']) ? $data['exceptionType'] : '';
    $exceptionReason = isset($data['exceptionReason']) ? $data['exceptionReason'] : '';
    if (empty($trackingNumbers)) {
        exit(json_encode(['code' => 1, 'msg' => '请选择要标记异常的快递单号']));
    }
    if (empty($exceptionType)) {
        exit(json_encode(['code' => 2, 'msg' => '请选择异常类型']));
    }
    
    $success = 0;
    $failed = 0;
    $uidCondition = implode(',', $allowedUids);
    
    foreach ($trackingNumbers as $trackingNumber) {
        $order = $DB->getRow("SELECT posting_number, uid FROM ozon_order WHERE (courierNumber = :trackingNumber OR tracking_number = :trackingNumber) AND uid IN ($uidCondition) LIMIT 1", [':trackingNumber' => $trackingNumber]);
        
        if (!$order) {
            $failed++;
            continue;
        }
        
        // 准备异常说明
        $notes = $exceptionReason;
        if (!empty($exceptionType)) {
            $typeName = '';
            switch($exceptionType) {
                case 'query_exception': $typeName = '查询异常'; break;
                case 'tracking_number_error': $typeName = '单号错误'; break;
                case 'other_exception': $typeName = '其他异常'; break;
                default: $typeName = $exceptionType;
            }
            $notes = "[{$typeName}] " . $notes;
        }
        
        // 向packing_records表插入记录
        $insertData = [
            ':uid' => $order['uid'] ?? 0,
            ':posting_number' => $order['posting_number'] ?? '',
            ':tracking_number' => $trackingNumber,
            ':courier_number' => $trackingNumber,
            ':packer_id' => $packerId,
            ':packing_status' => 0,
            ':notes' => $notes
        ];
        
        // 使用字段名与参数名完全一致的SQL
        $insertRes = $DB->exec("INSERT INTO packing_records 
                               (uid, posting_number, tracking_number, courierNumber, packerId, packing_status, notes) 
                               VALUES 
                               (:uid, :posting_number, :tracking_number, :courier_number, :packer_id, :packing_status, :notes)", 
                               $insertData);
        
        if ($insertRes !== false) {
            $success++;
        } else {
            $errorInfo = $DB->errorInfo();
            error_log('标记异常失败: ' . json_encode($errorInfo) . ', Data: ' . json_encode($insertData));
            $failed++;
        }
    }
    
    if ($success > 0) {
        exit(json_encode(['code' => 0, 'msg' => "标记异常成功: $success 条记录".($failed > 0 ? "，失败: $failed 条" : "")]));
    } else {
        exit(json_encode(['code' => 3, 'msg' => '标记异常失败']));
    }
break;
//打包员数据表
case 'get_list':
    if ($packerId <= 0) exit(json_encode(['code' => 401, 'msg' => '未登录']));
    
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(1, intval($_GET['limit'] ?? 10)));
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $where = "packerId = :packerId";
    $params = [':packerId' => $packerId];
    
    // 修复状态查询逻辑
    $searchFields = ['posting_number', 'courierNumber', 'packing_status'];
    foreach ($searchFields as $field) {
        if (!empty($_GET[$field])) {
            if ($field === 'packing_status') {
                $where .= " AND $field = :$field";
                $params[":$field"] = intval($_GET[$field]);
            } else {
                $where .= " AND $field LIKE :$field";
                $params[":$field"] = '%'.$_GET[$field].'%';
            }
        }
    }
    
    // 修复总数查询（字段名+取实际值）
    $totalRes = $DB->getRow("SELECT COUNT(*) as total FROM packing_records WHERE $where", $params);
    $total = $totalRes['total'] ?? 0;
    
    // 获取分页数据
    $data = $DB->getAll(
        "SELECT * FROM packing_records 
         WHERE $where 
         ORDER BY id DESC 
         LIMIT $offset, $limit",
        $params
    );
    
    // 移除冗余字段cs
    exit(json_encode([
        'code' => 0,
        'msg' => '',
        'count' => $total, // 直接返回整数
        'data' => $data
    ]));
break;
// 获取订单数据 - 仅查询关联用户的订单

// 获取订单数据 - 仅查询关联用户的订单
case 'get_orders':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取当前packerId关联的user_uid数组
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId LIMIT 1", [':packerId' => $packerId]);
    if (!$packerInfo || empty($packerInfo['user_uid'])) {
        exit(json_encode(['code' => 403, 'msg' => '无权访问该信息']));
    }
    
    // 解析user_uid - 支持JSON数组或逗号分隔的字符串格式
    $userUids = [];
    if (substr($packerInfo['user_uid'], 0, 1) === '[') {
        // JSON数组格式
        $decodedUids = json_decode($packerInfo['user_uid'], true);
        if (is_array($decodedUids)) {
            $userUids = $decodedUids;
        }
    } else {
        // 逗号分隔的字符串格式
        $userUids = explode(',', $packerInfo['user_uid']);
        // 移除可能的空白符
        $userUids = array_map('trim', $userUids);
    }
    
    if (empty($userUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无关联用户信息']));
    }
    
    // 构建查询条件
    $placeholders = implode(',', array_fill(0, count($userUids), '?'));
    $params = $userUids;
    
    $status = trim($_GET['status'] ?? '');

    $text = trim($_GET['text'] ?? '');
    $money = trim($_GET['money'] ?? '');
    $moneys = trim($_GET['moneys'] ?? '');
    $storeid = trim($_GET['storeid'] ?? '');
    $date1 = trim($_GET['date1'] ?? '');
    $date2 = trim($_GET['date2'] ?? '');
    $uid = trim($_GET['uid'] ?? '');
    

    
    $sql = " A.uid IN ($placeholders)";
    
    // UID筛选
    if ($uid) {
        // 检查该UID是否在允许的范围内
        if (in_array($uid, $userUids)) {
            $sql .= " AND A.uid = ?";
            $params[] = $uid;
        } else {
            // 如果输入的UID不在允许范围内，返回空结果
            exit(json_encode(['code' => 0, 'msg' => 'success', 'count' => 0, 'data' => []]));
        }
    }
    
    // 状态筛选
    if ($status && $status !== 'all') {
        if ($status == 'pending') {
            $sql .= " AND A.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'cancelled_from_split_pending')";
        } else if ($status == 'not_purchased') {
            $sql .= " AND (A.cost IS NULL OR A.cost = '') AND A.status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'cancelled_from_split_pending')";
        } else if ($status == 'purchased') {
            $sql .= " AND A.cost IS NOT NULL AND A.cost != '' AND (status = 'awaiting_packaging')";
        } else if ($status == 'awaiting_deliver') {
            $sql .= " AND A.status='awaiting_deliver' AND A.packing_status=0 AND cost IS NOT NULL AND cost != '' AND cost != 'NULL' AND manualstatus = 1";
        } else if ($status == 'awaiting_deliver2') {
            $sql .= " AND A.status='awaiting_deliver' AND A.packing_status=1";
        } else if ($status == 'cancelled') {
            $sql .= " AND (A.status='cancelled' OR A.status='cancelled_from_split_pending')";
        } else {
            $sql .= " AND A.status=?";
            $params[] = $status;
        }
    }

    
    // 文本搜索
    if ($text) {
        $sql .= " AND (A.order_name LIKE ? OR A.name2 LIKE ? OR A.posting_number=? OR A.sku=? OR A.purchase_orderSn=? OR A.courierNumber=?)";
        $searchText = "%{$text}%";
        $params = array_merge($params, [$searchText, $searchText, $text, $text, $text, $text]);
    }
    
    // 店铺筛选
    if ($storeid) {
        $sql .= " AND A.storeid=?";
        $params[] = $storeid;
    }
    
    // 价格区间
    if ($money || $moneys) {
        if ($money === '') $money = 0;
        if ($moneys === '') $moneys = 999999999;
        $sql .= " AND A.price >= ? AND A.price <= ?";
        $params[] = $money;
        $params[] = $moneys;
    }
    
    // 日期区间
    if ($date1 && $date2) {
        $sql .= " AND A.date >= ? AND A.date <= ?";
        $params[] = $date1;
        $params[] = $date2;
    }
    
    // 分页参数
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // 获取总记录数
    $total = $DB->getColumn("SELECT count(*) from ozon_order A WHERE{$sql}", $params);
    
    // 排序
    $sort_field = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'in_process_at';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    $orderBy = " ORDER BY A.{$sort_field} {$sort_order}";
    
    // 获取订单数据
    $finalSql = "SELECT A.*,B.storename,B.ClientId,C.stocks,C.commissions,A.offer_id,P.primary_image FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id LEFT JOIN ozon_products C ON A.sku=C.sku LEFT JOIN ozon_products P ON A.sku=P.sku WHERE{$sql}";
    $finalSql .= $orderBy . " LIMIT {$offset}, {$limit}";
    
    $list = $DB->getAll($finalSql, $params);
    
    // 处理订单数据
    $list2 = [];
    foreach ($list as $row) {
        // 检查是否有多个商品
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        
        // 处理库存和佣金信息
        $stocks = json_decode($row['stocks'], true);
        $commissions = json_decode($row['commissions'], true);
        
        if ($stocks && $commissions) {
            switch ($stocks['stocks'][0]['source']) {
                case 'fbo': $i = 0; break;
                case 'fbs': $i = 1; break;
                case 'rfbs': $i = 2; break;
                case 'fbp': $i = 3; break;
                default: $i = 0;
            }
            $row['percent'] = $commissions[$i]['percent'] ?? 18;
            $row['commissions'] = round($commissions[$i]['value'] * $row['quantity'], 2) . " ¥";
        }
        
        // 格式化日期
        $row['in_process_at'] = preg_replace('/^\d{4}-/', '', $row['in_process_at']);
        $row['shipment_date'] = preg_replace('/^\d{4}-/', '', $row['shipment_date']);
        
        // 调试信息：简化输出
        if (!empty($row['products'])) {
            error_log("Order " . $row['posting_number'] . ": products count = " . count($products));
        }
        
        // 添加多商品信息处理
        if (!empty($products) && is_array($products) && count($products) > 1) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
            error_log("Setting multi-product for order: " . $row['posting_number']);
            
            // 计算多商品订单的总价格
            $totalPrice = 0;
            $totalQuantity = 0;
            foreach ($products as $product) {
                $productPrice = floatval($product['price'] ?? 0);
                $productQuantity = intval($product['quantity'] ?? 1);
                $totalPrice += $productPrice * $productQuantity;
                $totalQuantity += $productQuantity;
            }
            
            // 更新订单的总价格和总数量
            $row['calculated_total_price'] = number_format($totalPrice, 2, '.', '');
            $row['calculated_total_quantity'] = $totalQuantity;
            
            // 为每个商品添加图片信息
            foreach ($row['products_info'] as $key => $product) {
                // 尝试从产品表中获取图片
                $product_info = $DB->getRow("SELECT primary_image FROM ozon_products WHERE sku = ?", [$product['sku']]);
                if ($product_info && !empty($product_info['primary_image'])) {
                    $row['products_info'][$key]['image'] = $product_info['primary_image'];
                    
                    // 如果是第一个商品且主图为空，则使用该商品的图片作为主图
                    if ($key === 0 && empty($row['primary_image'])) {
                        $row['primary_image'] = $product_info['primary_image'];
                    }
                } else {
                    // 如果没有找到图片，使用默认图片
                    $row['products_info'][$key]['image'] = '../assets/img/syncing.png';
                }
            }
        } else {
            $row['has_multiple_products'] = false;
        }
        
        // 处理图片和商品链接信息
        if (empty($row['primary_image']) || $row['primary_image'] === '../assets/img/syncing.png') {
            $row['primary_image'] = '../assets/img/syncing.png';
        }
        
        // 构建商品链接
        if (!empty($row['offer_id'])) {
            $row['product_url'] = 'https://www.ozon.ru/product/' . $row['offer_id'];
        } else {
            $row['product_url'] = '';
        }
        
        $list2[] = $row;
    }
    
    exit(json_encode(['code' => 0, 'msg' => 'success', 'count' => $total, 'data' => $list2]));
break;
// 获取订单状态统计 - 仅统计关联用户的订单
case 'order_status_counts':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取当前packerId关联的user_uid数组
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId LIMIT 1", [':packerId' => $packerId]);
    if (!$packerInfo || empty($packerInfo['user_uid'])) {
        exit(json_encode(['code' => 403, 'msg' => '无权访问该信息']));
    }
    
    // 解析user_uid
    $userUids = [];
    if (substr($packerInfo['user_uid'], 0, 1) === '[') {
        $decodedUids = json_decode($packerInfo['user_uid'], true);
        if (is_array($decodedUids)) {
            $userUids = $decodedUids;
        }
    } else {
        $userUids = explode(',', $packerInfo['user_uid']);
        $userUids = array_map('trim', $userUids);
    }
    
    if (empty($userUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无关联用户信息']));
    }
    
    $placeholders = implode(',', array_fill(0, count($userUids), '?'));
    
    $counts = [];
    
    // 待处理
    $counts['pending'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'cancelled_from_split_pending')", $userUids);
    
    // 未采购
    $counts['not_purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'cancelled_from_split_pending')", $userUids);
    
    // 已采购
    $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND cost IS NOT NULL AND cost != '' AND (status = 'awaiting_packaging')", $userUids);
    
    // 等待发货
    $counts['awaiting_deliver'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status = 'awaiting_deliver' AND packing_status = 0 AND cost IS NOT NULL AND cost != '' AND cost != 'NULL' AND manualstatus = 1", $userUids);
    
    // 交运平台
    $counts['awaiting_deliver2'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status = 'awaiting_deliver' AND packing_status = 1", $userUids);
    
    // 运输中
    $counts['delivering'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status = 'delivering'", $userUids);
    
    // 已送达
    $counts['delivered'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status = 'delivered'", $userUids);
    
    // 已取消
    $counts['cancelled'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND status = 'cancelled'", $userUids);
    
    // 全部订单 (除已取消)
    $counts['all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid IN ($placeholders) AND (status!='cancelled' OR status!='cancelled_from_split_pending')", $userUids);
    
    // 等待发货的库存调用统计
    $awaitingDeliverCondition = "uid IN ($placeholders) AND status = 'awaiting_deliver' AND packing_status = 0 AND cost IS NOT NULL AND cost != '' AND cost != 'NULL'";
    
    // 等待发货 - 全部
    $counts['outbound_all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE $awaitingDeliverCondition", $userUids);
    
    // 等待发货 - 已调用库存
    $counts['outbound_yes'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE $awaitingDeliverCondition AND Outbound_data IS NOT NULL AND Outbound_data != '' AND Outbound_data != 'null' AND Outbound_data != '0000-00-00 00:00:00'", $userUids);
    
    // 等待发货 - 未调用库存
    $counts['outbound_no'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE $awaitingDeliverCondition AND (Outbound_data IS NULL OR Outbound_data = '' OR Outbound_data = 'null' OR Outbound_data = '0000-00-00 00:00:00')", $userUids);
    
    exit(json_encode(['code' => 0, 'data' => $counts]));
break;

// 获取打包员信息
case 'get_packer_info':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    $packerInfo = $DB->getRow("SELECT packerId, username, user_uid FROM packers WHERE packerId = :packerId LIMIT 1", [':packerId' => $packerId]);
    if (!$packerInfo || empty($packerInfo['user_uid'])) {
        exit(json_encode(['code' => 403, 'msg' => '无权访问该信息']));
    }
    
    exit(json_encode(['code' => 0, 'data' => $packerInfo]));
break;

// 获取店铺列表 - 仅获取关联用户的店铺
case 'get_stores':
    if ($packerId <= 0) {
        exit(json_encode(['code' => 401, 'msg' => '未登录']));
    }
    
    // 获取当前packerId关联的user_uid数组
    $packerInfo = $DB->getRow("SELECT user_uid FROM packers WHERE packerId = :packerId LIMIT 1", [':packerId' => $packerId]);
    if (!$packerInfo || empty($packerInfo['user_uid'])) {
        exit(json_encode(['code' => 403, 'msg' => '无权访问该信息']));
    }
    
    // 解析user_uid
    $userUids = [];
    if (substr($packerInfo['user_uid'], 0, 1) === '[') {
        $decodedUids = json_decode($packerInfo['user_uid'], true);
        if (is_array($decodedUids)) {
            $userUids = $decodedUids;
        }
    } else {
        $userUids = explode(',', $packerInfo['user_uid']);
        $userUids = array_map('trim', $userUids);
    }
    
    if (empty($userUids)) {
        exit(json_encode(['code' => 403, 'msg' => '无关联用户信息']));
    }
    
    $placeholders = implode(',', array_fill(0, count($userUids), '?'));
    
    $stores = $DB->getAll("SELECT id, storename FROM ozon_store WHERE uid IN ($placeholders) ORDER BY storename", $userUids);
    
    exit(json_encode(['code' => 0, 'data' => $stores]));
break;

// 获取商品图片的函数
function getProductImageFromAPI($sku, $clientId, $key) {
    try {
        if (empty($sku) || empty($clientId) || empty($key)) {
            return null;
        }
        
        $client = new \lib\OzonApiClient($clientId, $key);
        $product_info = $client->getOzonProductsid(['sku'=>[$sku]]);
        //exit(json_encode($product_info));
        if ($product_info && isset($product_info['data']) && isset($product_info['data']['items'])) {
            return ['image' => $product_info['data']['items'][0]['primary_image']];
        } else if ($product_info && isset($product_info['result']) && isset($product_info['result']['primary_image'])) {
            return ['image' => $product_info['result']['primary_image']];
        } else if ($product_info && isset($product_info['result']) && isset($product_info['result']['images']) && !empty($product_info['result']['images'])) {
            return ['image' => $product_info['result']['images'][0]];
        }
    } catch (Exception $e) {
        error_log("Error getting product image from API: " . $e->getMessage());
    }
    
    return null;
}


default:
    exit(json_encode(['code' => 404, 'msg' => '无效的操作']));
}



?>
