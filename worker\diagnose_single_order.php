<?php
/**
 * 单个订单诊断脚本
 * 专门用于诊断特定订单号的查询和更新过程
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

$postingNumber = $argv[1] ?? '*************-3';

echo "=== 订单诊断工具 ===\n";
echo "订单号: {$postingNumber}\n";
echo "时间: " . date('Y-m-d H:i:s') . "\n\n";

// 1. 精确查询
echo "1. 精确查询结果:\n";
$exactOrder = $DB->find('ozon_order', ['posting_number', 'status', 'storeid', 'uid', 'order_id', 'in_process_at', 'created_at'], 
    ['posting_number' => $postingNumber]);

if ($exactOrder) {
    echo "  ✓ 订单存在\n";
    echo "  订单号: {$exactOrder['posting_number']}\n";
    echo "  状态: {$exactOrder['status']}\n";
    echo "  店铺ID: {$exactOrder['storeid']}\n";
    echo "  用户ID: {$exactOrder['uid']}\n";
    echo "  订单ID: {$exactOrder['order_id']}\n";
    echo "  创建时间: {$exactOrder['created_at']}\n";
    echo "  处理时间: {$exactOrder['in_process_at']}\n";
} else {
    echo "  ✗ 订单不存在\n";
}

// 2. 模糊查询（尝试找相似订单号）
echo "\n2. 模糊查询（相似订单号）:\n";
$similarOrders = $DB->getAll("SELECT posting_number, status, storeid FROM ozon_order WHERE posting_number LIKE ? LIMIT 5", 
    ['%' . substr($postingNumber, -6) . '%']);

if ($similarOrders) {
    echo "  找到相似订单号:\n";
    foreach ($similarOrders as $similar) {
        echo "    - {$similar['posting_number']} (状态: {$similar['status']}, 店铺: {$similar['storeid']})\n";
    }
} else {
    echo "  没有找到相似订单号\n";
}

// 3. 检查订单号格式
echo "\n3. 订单号格式分析:\n";
echo "  长度: " . strlen($postingNumber) . " 字符\n";
echo "  格式: " . (preg_match('/^\d{8}-\d{4}-\d$/', $postingNumber) ? '标准格式' : '非标准格式') . "\n";

// 4. 查询该店铺的其他订单（如果有店铺信息）
if ($exactOrder && $exactOrder['storeid']) {
    echo "\n4. 同店铺其他订单（最近10个）:\n";
    $storeOrders = $DB->getAll("SELECT posting_number, status, in_process_at FROM ozon_order WHERE storeid = ? AND posting_number != ? ORDER BY in_process_at DESC LIMIT 10", 
        [$exactOrder['storeid'], $postingNumber]);
    
    if ($storeOrders) {
        foreach ($storeOrders as $order) {
            echo "    - {$order['posting_number']} (状态: {$order['status']})\n";
        }
    } else {
        echo "    没有找到同店铺的其他订单\n";
    }
}

// 5. 测试更新操作（模拟，不实际更新）
echo "\n5. 更新操作测试:\n";
if ($exactOrder) {
    $currentStatus = $exactOrder['status'];
    $newStatus = 'delivered';  // 示例新状态
    
    if ($currentStatus === $newStatus) {
        echo "  状态无需更新 ({$currentStatus})\n";
    } else {
        echo "  需要更新: {$currentStatus} → {$newStatus}\n";
        echo "  SQL: UPDATE ozon_order SET status = '{$newStatus}' WHERE posting_number = '{$postingNumber}'\n";
        
        // 如果需要实际更新，取消下面的注释
        // $updateResult = $DB->update('ozon_order', ['status' => $newStatus], ['posting_number' => $postingNumber]);
        // echo "  更新结果: " . ($updateResult ? '成功' : '失败') . "\n";
    }
} else {
    echo "  无法更新，订单不存在\n";
}

// 6. 检查数据库连接和表结构
echo "\n6. 数据库检查:\n";
try {
    $tableExists = $DB->query("SHOW TABLES LIKE 'ozon_order'");
    if ($tableExists && $DB->count($tableExists) > 0) {
        echo "  ✓ ozon_order 表存在\n";
        
        // 检查关键字段
        $columns = $DB->getAll("SHOW COLUMNS FROM ozon_order WHERE Field IN ('posting_number', 'status', 'storeid')");
        echo "  关键字段:\n";
        foreach ($columns as $col) {
            echo "    - {$col['Field']}: {$col['Type']}\n";
        }
    } else {
        echo "  ✗ ozon_order 表不存在\n";
    }
} catch (Exception $e) {
    echo "  ✗ 数据库连接错误: " . $e->getMessage() . "\n";
}

echo "\n=== 诊断完成 ===\n";