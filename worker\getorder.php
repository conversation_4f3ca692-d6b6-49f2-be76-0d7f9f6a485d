<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 内存和性能设置
ini_set('memory_limit', '512M');
set_time_limit(0);

class SafeConsumer {
    private $connection;
    private $channel;
    private $maxRetries = 3;
    private $logger;
    private $redis;
    private $config;
    private $db;
    
    public function __construct($config, $DB) {
        $this->config = $config;
        $this->logger = new ConsumerLogger();
        $this->redis  = new Redis();
        $this->connectRabbitMQ();
        $this->db = $DB;
    }
    
    private function connectRabbitMQ() {
        try {
            $this->connection = new AMQPStreamConnection(
                $this->config['host'],
                $this->config['port'],
                $this->config['user'],
                $this->config['pwd'],
                '/',
                false,
                'AMQPLAIN',
                null,
                'en_US',
                60,
                30,
                null,
                true
            );
            
            $this->channel = $this->connection->channel();
            // 声明三个队列，分别对应不同类型的任务
            $this->channel->queue_declare('product_full_task', false, true, false, false); // 综合任务
            $this->channel->queue_declare('product_info_task', false, true, false, false);  // 获取规格和图片
            $this->channel->queue_declare('product_commission_task', false, true, false, false); // 获取佣金
            $this->channel->queue_declare('product_Translated_title', false, true, false, false); // 获取翻译的标题
            $this->channel->queue_declare('product_images_task', false, true, false, false); //获取图片数组
            $this->channel->basic_qos(null, 1, null);
            
        } catch (Exception $e) {
            $this->logger->error("RabbitMQ连接失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    public function consume() {
        // 综合任务处理回调
        $fullTaskCallback = function ($msg) {
            $this->processTask($msg, 'full');
        };
        
        // 获取规格和图片任务处理回调
        $infoTaskCallback = function ($msg) {
            $this->processTask($msg, 'info');
        };
        
        // 获取佣金任务处理回调
        $commissionTaskCallback = function ($msg) {
            $this->processTask($msg, 'commission');
        };
        
        // 获取翻译的标题
        $TranslatedtitleCallback = function ($msg) {
            $this->processTask($msg, 'Translated_title');
        };
        
        // 获取多产品图片
        $imagesTaskCallback = function ($msg) {
            $this->processTask($msg, 'images');
        };
        
        // 绑定消费者到不同的队列
        $this->channel->basic_consume('product_full_task', '', false, false, false, false, $fullTaskCallback);
        $this->channel->basic_consume('product_info_task', '', false, false, false, false, $infoTaskCallback);
        $this->channel->basic_consume('product_commission_task', '', false, false, false, false, $commissionTaskCallback);
        $this->channel->basic_consume('product_Translated_title', '', false, false, false, false, $TranslatedtitleCallback);
        $this->channel->basic_consume('product_images_task', '', false, false, false, false, $imagesTaskCallback);
        
        $this->logger->info("消费者启动，等待消息...");
        
        while (count($this->channel->callbacks)) {
            $this->channel->wait();
        }
    }
    
    private function processTask($msg, $taskType) {
        $startTime = microtime(true);
        $messageId = uniqid();
        
        try {
            $this->logger->info("[$messageId] 开始处理{$taskType}任务");
            
            $data = $this->parseMessage($msg->body);
            $this->logger->info("[$messageId] 收到SKU: ".$data['sku']);
            
            $this->processMessageWithRetry($messageId, $data, $taskType);
            
            $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
            
            $duration = round(microtime(true) - $startTime, 2);
            $this->logger->info("[$messageId] 处理完成 耗时: {$duration}s");
            
        } catch (InvalidMessageException $e) {
            $this->logger->error("[$messageId] 消息格式错误: ".$e->getMessage());
            $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
            
        } catch (RecoverableException $e) {
            $this->logger->error("[$messageId] 可重试错误: ".$e->getMessage());
            $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag']);
            
        } catch (Exception $e) {
            $this->logger->error("[$messageId] 不可恢复错误: ".$e->getMessage());
            $msg->delivery_info['channel']->basic_reject($msg->delivery_info['delivery_tag'], false);
        }
        
        $this->checkResourceUsage($messageId);
    }
    
    private function parseMessage($body) {
        $data = json_decode($body, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new InvalidMessageException("无效的JSON数据");
        }
        
        if (empty($data['sku'])) {
            throw new InvalidMessageException("消息缺少sku字段");
        }
        
        if (empty($data['storeid'])) {
            throw new InvalidMessageException("消息缺少storeid字段");
        }
        
        return $data;
    }
    
    private function processMessageWithRetry($messageId, $data, $taskType) {
        $retryCount = 0;
        $lastError = null;
        
        while ($retryCount < $this->maxRetries) {
            try {
                $this->processProductData($data, $taskType);
                return;
                
            } catch (RecoverableException $e) {
                $retryCount++;
                $lastError = $e;
                $waitTime = pow(2, $retryCount);
                $this->logger->warning("[$messageId] 尝试 {$retryCount}/{$this->maxRetries} 失败，等待 {$waitTime}s 后重试");
                sleep($waitTime);
            }
        }
        
        throw new Exception("重试 {$this->maxRetries} 次后仍然失败: ".$lastError->getMessage());
    }
    
    private function processProductData($data, $taskType) {
        $store = $this->getStoreInfo($data['storeid']);
        if (!$store) {
            throw new Exception("找不到店铺ID为 {$data['storeid']} 的店铺信息");
        }
        
        $client = new \lib\OzonApiClient($store['ClientId'], $store['key']);
        
        // 根据任务类型执行不同的处理
        switch ($taskType) {
            case 'full':
                $this->processFullTask($client, $data);
                break;
                
            case 'info':
                $this->processInfoTask($client, $data);
                break;
            case 'commission':
                $this->processCommissionTask($client, $data);
                break;
            case 'Translated_title':
                $this->Translated_title($client, $data);
                break;
            case 'images':
                $this->getAllimages($client, $data, $store);
                break;
            default:
                throw new InvalidMessageException("未知的任务类型: {$taskType}");
        }
    }
    
    private function processFullTask($client, $data) {
        // 处理综合任务 - 获取规格、图片和佣金
        $characteristics = $this->getProductCharacteristics($client, $data);
        $productInfo = $this->getProductInfo($client, $data['sku']);
        
        $this->saveProductData($data, $characteristics, $productInfo);
        $this->Translated_title($client, $data);
    }
    
    private function processInfoTask($client, $data) {
        // 处理获取规格和图片任务
        $characteristics = $this->getProductCharacteristics($client, $data);
        $this->saveProductInfo($data, $characteristics);
    }
    
    private function processCommissionTask($client, $data) {
        // 处理获取佣金任务
        try {
            // 首先检查Redis中是否已有佣金数据
            $cachedCommission = $this->getredis($data['sku']);
            if($cachedCommission && $cachedCommission > 0){
                // 如果Redis中有数据，直接更新数据库
                $this->logger->info("从Redis缓存获取佣金: SKU={$data['sku']}, 佣金={$cachedCommission}%");
                $this->saveCommission($data);
                return;
            }
            
            // Redis中没有数据，调用API获取
            $this->logger->info("开始从API获取佣金信息: SKU={$data['sku']}");
            $productInfo = $this->getProductInfo($client, $data['sku']);
            
            if(empty($productInfo)) {
                $this->logger->error("API返回空的商品信息: SKU={$data['sku']}");
                throw new RecoverableException("获取商品信息失败: SKU={$data['sku']}");
            }
            
            // 保存佣金信息到Redis和数据库
            $this->saveProductCommission($data, $productInfo);
            
        } catch (Exception $e) {
            $this->logger->error("获取佣金失败: SKU={$data['sku']}, 错误: " . $e->getMessage());
            throw $e;
        }
    }
    
    private function Translated_title($client, $data){
        // 处理翻译商品标题
        if(empty($data['order_name'])){
            $this->redis->connect('127.0.0.1', 6379);
            $rsdisplay = $this->redis->get($data['sku']."_Translated_title");
            if(empty($rsdisplay)){
                $name = Transmart($data['name2']);
                if($name){
                    error_log("处理翻译商品标题：".$name); // 调试日志
                    $this->redis->set($data['sku']."_Translated_title", $name, 3600);
                    $this->db->update('order', ['order_name'=>$name], ['posting_number' => $data['posting_number']]);
                }else{
                    
                    throw new RecoverableException("翻译商品标题失败");
                }
            }else{
                error_log("缓存商品标题：".$rsdisplay); // 调试日志
                $this->db->update('order', ['order_name'=>$rsdisplay], ['posting_number' => $data['posting_number']]);
            }
            $this->redis->close();
        }
    }
    
    private function getAllimages($client, $data, $store){
        if(isset($data['products'])){
            $products = json_decode($data['products'], true);
            if (!empty($products) && is_array($products) && count($products) > 1) {
                // 为每个商品添加图片信息
                $images = [];
                // 尝试从产品表中获取图片
                foreach ($products as $key => $product) {
                    
                    $api_product = getProductImageFromAPI($product['sku'], $store['ClientId'], $store['key']);
                    //exit(json_encode($api_product));
                    if ($api_product && !empty($api_product['image'])) {
                        error_log("商品图片获取成功：".$api_product['image'][0]); // 调试日志
                        $images[] = $api_product['image'][0];
                    }
                    
                }
                if(isset($images)){
                    if($this->db->update('order', ['primary_image' => implode(',', $images)], ['posting_number' => $data['posting_number']])){
                        error_log("商品图片保存成功"); // 调试日志
                    }else{
                        error_log("商品图片保存失败"); // 调试日志
                    }
                }else{
                    error_log("商品图片没有可保存的数据"); // 调试日志
                }
            }
        }else{
            error_log("商品图片...........异常"); // 调试日志
        }
        return true;
    }
    
    private function getProductCharacteristics($client, $data) {
        $result = $client->characteristics($data);
        
        if (empty($result['result'][0])) {
            throw new RecoverableException("获取商品特性失败");
        }
        
        return $result['result'][0];
    }
    
    private function getProductInfo($client, $sku) {
        
        $result = $client->productinfolist([$sku], 'sku');
        $result = json_decode($result, true);
        
        if (empty($result['items'][0])) {
            throw new RecoverableException("获取商品信息失败");
        }
        
        return $result['items'][0];
    }
    
    private function saveProductData($data, $characteristics, $productInfo) {
        $this->saveProductInfo($data, $characteristics);
        if($this->getredis($data['sku'])){
            $this->saveCommission($data);
        }else{
            $this->saveProductCommission($data, $productInfo);
        }
    }
    
    private function saveCommission($data){
        $ca = $this->getredis($data['sku']);
        if($ca){
            $updateData['commission_percent'] = $ca;
            $this->db->update('order', $updateData, ['sku' => $data['sku']]);
        }
    }
    
    private function saveProductInfo($data, $characteristics) {
        $jsonurl = ROOT.'/assets/json/特征数据库/' . $data['sku'] . '.json';
        $updateData = ['primary_image' => $characteristics['primary_image']];
        
        foreach ($characteristics['attributes'] as $item) {
            if ($item['id'] == 10096) {          // 颜色
                $ii = 0;
                $color = '';
                foreach ($item['values'] as $x) {
                    $colorInfo = attribute_id_name(10096, $x['value']);
                    if ($colorInfo) {
                        if ($ii >= 1) {
                            $color .= '、' . $colorInfo['name'];
                        } else {
                            $color = $colorInfo['name'];
                        }
                        $ii++;
                    }
                }
                $updateData['color'] = $color;
            } else if ($item['id'] == 4382) {     // 尺寸
                $updateData['dimensions'] = $item['values'][0]['value'];
            } else if ($item['id'] == 10051) {    // 材质
                $updateData['material'] = Transmart($item['values'][0]['value']);
            } elseif ($item['id'] == 4497) {
                $updateData['weight'] = $item['values'][0]['value'];
            } elseif ($item['id'] == 4295) {
                $updateData['size'] = $item['values'][0]['value'];
            } elseif ($item['id'] == 9814) {
                $updateData['num'] = $item['values'][0]['value'];
            }
        }
        
        if (empty($updateData['weight'])) {
            $updateData['weight'] = $characteristics['weight'];
        }
        
        //weightdata($data['sku'], $characteristics);
        $this->db->update('order', $updateData, ['sku' => $data['sku']]);
        file_put_contents($jsonurl, json_encode($characteristics, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES));
    }
    
    private function saveProductCommission($data, $productInfo) {
        $updateData = [];
        
        // 记录API返回的完整信息用于调试
        $this->logger->debug("API返回的商品信息: SKU={$data['sku']}, " . json_encode($productInfo, JSON_UNESCAPED_UNICODE));
        
        // 处理库存信息
        if (isset($productInfo['stocks']) && !empty($productInfo['stocks']['stocks'])) {
            switch ($productInfo['stocks']['stocks'][0]['source']) {
                case 'fbo':
                    $i = 0;
                    break;
                case 'fbs':
                    $i = 1;
                    break;
                case 'rfbs':
                    $i = 2;
                    break;
                case 'fbp':
                    $i = 3;
                    break;
                default:
                    $i = 0;
            }
            
            try {
                $this->db->update('products', ['stocks' => json_encode($productInfo['stocks'])], ['sku' => $data['sku']]);
                $this->logger->debug("更新库存信息成功: SKU={$data['sku']}");
            } catch (Exception $e) {
                $this->logger->error("更新库存信息失败: SKU={$data['sku']}, 错误: " . $e->getMessage());
            }
        }
        
        // 处理佣金信息
        if (isset($productInfo['commissions']) && !empty($productInfo['commissions']) && 
            isset($productInfo['commissions'][0]['percent']) && 
            $productInfo['commissions'][0]['percent'] > 0) {
            
            $commission = $productInfo['commissions'][0]['percent'];
            $this->logger->info("获取到佣金信息: SKU={$data['sku']}, 佣金={$commission}%");
            
            try {
                // 保存到Redis缓存
                $this->setredis($data['sku'], $commission);
                $this->logger->debug("佣金保存到Redis成功: SKU={$data['sku']}, 佣金={$commission}%");
                
                // 更新数据库中所有相同SKU的订单
                $updateData['commission_percent'] = $commission;
                $affectedRows = $this->db->update('order', $updateData, ['sku' => $data['sku']]);
                $this->logger->info("更新数据库佣金成功: SKU={$data['sku']}, 佣金={$commission}%, 影响订单数={$affectedRows}");
                
            } catch (Exception $e) {
                $this->logger->error("保存佣金信息失败: SKU={$data['sku']}, 错误: " . $e->getMessage());
                throw $e;
            }
        } else {
            $this->logger->warning("API返回的商品信息中没有有效的佣金数据: SKU={$data['sku']}");
            
            // 检查API响应结构
            if (!isset($productInfo['commissions'])) {
                $this->logger->warning("API响应中缺少commissions字段: SKU={$data['sku']}");
            } elseif (empty($productInfo['commissions'])) {
                $this->logger->warning("API响应中commissions字段为空: SKU={$data['sku']}");
            } elseif (!isset($productInfo['commissions'][0]['percent'])) {
                $this->logger->warning("API响应中缺少佣金百分比: SKU={$data['sku']}");
            } else {
                $this->logger->warning("佣金百分比无效: SKU={$data['sku']}, 值=" . $productInfo['commissions'][0]['percent']);
            }
        }
    }
    
    private function getStoreInfo($storeId) {
        $this->logger->debug("获取店铺信息: 店铺ID {$storeId}");
        
        try {
            $store = $this->db->find('store', '*', ['id' => $storeId]);
            if (!$store) {
                throw new Exception("找不到店铺ID为 {$storeId} 的店铺");
            }
            
            if (empty($store['ClientId']) || empty($store['key'])) {
                throw new Exception("店铺API凭证不完整");
            }
            
            return $store;
            
        } catch (Exception $e) {
            $this->logger->error("获取店铺信息失败: ".$e->getMessage());
            throw $e;
        }
    }
    
    private function checkResourceUsage($messageId) {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = ini_get('memory_limit');
        
        if ($memoryUsage > 100 * 1024 * 1024) {
            $this->logger->warning("[$messageId] 内存使用过高: ".round($memoryUsage/1024/1024, 2)."MB");
        }
    }
    
    private function getredis($sku){
        $this->redis->connect('127.0.0.1', 6379);
        $rsdisplay = $this->redis->get("{$sku}_commission");
        $this->redis->close();
        
        if ($rsdisplay === false) {
            return false;
        }
        $decoded = json_decode($rsdisplay, true);
        if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
            return $decoded;
        }
        return $rsdisplay;
    }
    
    private function setRedis($sku, $value, $type=1, $time = 172800)
    {
        $this->redis->connect('127.0.0.1', 6379);
        if($type==1){
            $this->redis->set("{$sku}_commission", $value, $time);
        }else{
            $this->redis->set("{$sku}_commission", $value, $time);
        }
        $this->redis->close();
    }
    
    public function __destruct() {
        try {
            if ($this->channel && $this->channel->is_open()) {
                $this->channel->close();
            }
            
            if ($this->connection && $this->connection->isConnected()) {
                $this->connection->close();
            }
        } catch (Exception $e) {
            $this->logger->error("关闭连接时出错: ".$e->getMessage());
        }
    }
}

// 自定义异常类
class InvalidMessageException extends Exception {}
class RecoverableException extends Exception {}

// 日志类
class ConsumerLogger {
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    private function log($level, $message, $context = []) {
        $logEntry = sprintf(
            "[%s] [%s] %s %s\n",
            date('Y-m-d H:i:s'),
            $level,
            $message,
            !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''
        );
        
        file_put_contents(__DIR__.'/logs/consumer.log', $logEntry, FILE_APPEND);
        echo $logEntry;
    }
}

// 启动消费者
try {
    
    $consumer = new SafeConsumer($Raconfig, $DB);
    $consumer->consume();
} catch (Exception $e) {
    echo "消费者启动失败: ".$e->getMessage()."\n";
    exit(1);
}