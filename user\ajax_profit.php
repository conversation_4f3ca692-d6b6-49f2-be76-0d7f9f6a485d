<?php
include("../includes/common.php");

header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Authorization");
@header('Content-Type: application/json; charset=UTF-8');

// 简单导出日志，便于排查 500 问题
if (!function_exists('export_profit_log')) {
    function export_profit_log($message) {
        $dir = __DIR__ . '/logs';
        if (!is_dir($dir)) {
            @mkdir($dir, 0775, true);
        }
        $file = $dir . '/export_profit.log';
        $line = '[' . date('Y-m-d H:i:s') . '] ' . (is_string($message) ? $message : json_encode($message, JSON_UNESCAPED_UNICODE)) . "\n";
        @file_put_contents($file, $line, FILE_APPEND);
    }
}

$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;

// 检查登录状态
if ($islogin2 != 1) {
    exit(json_encode(['code' => -3, 'msg' => '未登录']));
}

$uid = $userrow['uid'];

// 处理运输中状态订单的佣金计算
function calculateDeliveringCommission($item, $price, $custom_commission_rate = null, $quantity = 1) {
    $commission_cny = 0;
    
    if (empty($item['t_commission']) || floatval($item['t_commission']) == 0) {
        $commissionPercent = null;
        
        // 优先使用自定义佣金率
        if ($custom_commission_rate !== null && $custom_commission_rate > 0) {
            $commissionPercent = $custom_commission_rate;
        } else {
            // 使用原有逻辑获取佣金率
            if (isset($item['o_commission_percent']) && $item['o_commission_percent'] !== null && $item['o_commission_percent'] !== '') {
                $commissionPercent = floatval($item['o_commission_percent']);
            }
            if (($commissionPercent === null || $commissionPercent == 0) && !empty($item['sku'])) {
                $redisKey = $item['sku'] . "_commission";
                $cachedPercent = getRedis($redisKey);
                if ($cachedPercent !== false && $cachedPercent !== null && $cachedPercent !== '') {
                    $commissionPercent = floatval($cachedPercent);
                }
            }
        }
        
        if ($commissionPercent !== null && $commissionPercent != 0) {
            $fallbackCommission = floatval($price) * floatval($quantity) * ($commissionPercent / 100);
            $commission_cny = -1 * abs(round($fallbackCommission, 2));
        }
    }
    
    return $commission_cny;
}

switch ($act) {
    case 'get_profit_list':
        // 获取利润统计明细列表
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
        $offset = ($page - 1) * $limit;
        
        // 筛选条件
        $start_date = isset($_GET['start_date']) ? $_GET['start_date'] : '';
        $end_date = isset($_GET['end_date']) ? $_GET['end_date'] : '';
        $delivery_start_date = isset($_GET['delivery_start_date']) ? $_GET['delivery_start_date'] : '';
        $delivery_end_date = isset($_GET['delivery_end_date']) ? $_GET['delivery_end_date'] : '';
        $status = isset($_GET['status']) ? $_GET['status'] : '';
        $group_id = isset($_GET['group_id']) ? (int)$_GET['group_id'] : 0;
        $store_id = isset($_GET['store_id']) ? (int)$_GET['store_id'] : 0;
        $sku = isset($_GET['sku']) ? trim($_GET['sku']) : '';
        $posting_numbers = isset($_GET['posting_numbers']) ? trim($_GET['posting_numbers']) : '';
        $profit_rate_min = isset($_GET['profit_rate_min']) && $_GET['profit_rate_min'] !== '' ? (float)$_GET['profit_rate_min'] : null;
        $profit_rate_max = isset($_GET['profit_rate_max']) && $_GET['profit_rate_max'] !== '' ? (float)$_GET['profit_rate_max'] : null;
        $has_refund = isset($_GET['has_refund']) ? $_GET['has_refund'] : '';
        $custom_commission_rate = isset($_GET['custom_commission_rate']) && $_GET['custom_commission_rate'] !== '' ? (float)$_GET['custom_commission_rate'] : null;
        
        // 构建查询条件
        $where_conditions = ['o.uid = ?'];
        $params = [$uid];
        
        if (!empty($start_date)) {
            $where_conditions[] = 'DATE(o.in_process_at) >= ?';
            $params[] = $start_date;
        }
        
        if (!empty($end_date)) {
            $where_conditions[] = 'DATE(o.in_process_at) <= ?';
            $params[] = $end_date;
        }
        
        if (!empty($status)) {
            // 支持多个状态：逗号分隔的状态列表
            if (strpos($status, ',') !== false) {
                $status_array = array_filter(array_map('trim', explode(',', $status)));
                if (!empty($status_array)) {
                    $placeholders = str_repeat('?,', count($status_array) - 1) . '?';
                    $where_conditions[] = 'o.status IN (' . $placeholders . ')';
                    $params = array_merge($params, $status_array);
                }
            } else {
                $where_conditions[] = 'o.status = ?';
                $params[] = $status;
            }
        }
        
        if ($store_id > 0) {
            $where_conditions[] = 'o.storeid = ?';
            $params[] = $store_id;
        } elseif ($group_id > 0) {
            // 如果选择了分组，查询该分组下的所有店铺
            $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
            $shop_groups = $user && $user['shop_groups'] 
                ? json_decode($user['shop_groups'], true) 
                : ['groups' => [], 'defaultGroupId' => null];
            
            $shopIds = [];
            foreach ($shop_groups['groups'] ?? [] as $group) {
                if ($group['id'] == $group_id && !empty($group['shopIds'])) {
                    $shopIds = array_map('intval', $group['shopIds']);
                    break;
                }
            }
            
            if (!empty($shopIds)) {
                $placeholders = str_repeat('?,', count($shopIds) - 1) . '?';
                $where_conditions[] = 'o.storeid IN (' . $placeholders . ')';
                $params = array_merge($params, $shopIds);
            } else {
                // 如果分组下没有店铺，返回空结果
                $where_conditions[] = 'o.storeid = -1';
            }
        }
        
        if (!empty($sku)) {
            $where_conditions[] = 'o.sku LIKE ?';
            $params[] = '%' . $sku . '%';
        }
        
        if (!empty($posting_numbers)) {
            // 处理多行订单号输入，兼容 Windows/Mac 换行\r\n/\r
            $normalized = str_replace(["\r\n", "\r"], "\n", $posting_numbers);
            $posting_array = array_filter(array_map('trim', explode("\n", $normalized)));
            if (!empty($posting_array)) {
                $placeholders = str_repeat('?,', count($posting_array) - 1) . '?';
                $where_conditions[] = 'o.posting_number IN (' . $placeholders . ')';
                $params = array_merge($params, $posting_array);
            }
        }
        
        if (!empty($delivery_start_date)) {
            $where_conditions[] = 'o.delivering_date >= ?';
            $params[] = $delivery_start_date;
        }
        
        if (!empty($delivery_end_date)) {
            $where_conditions[] = 'o.delivering_date <= ?';
            $params[] = $delivery_end_date;
        }
        
        if ($has_refund === '1') {
            // 有退款：退款金额不为null且不为0（包括负数）
            $where_conditions[] = 'p.return_amount IS NOT NULL AND p.return_amount != 0';
        } elseif ($has_refund === '0') {
            // 无退款：退款金额为null或为0
            $where_conditions[] = '(p.return_amount IS NULL OR p.return_amount = 0)';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        // 获取汇率
        try {
            $exchange_rate_data = ForeignExchange(1, '人民币');
            $rub_to_cny_rate = isset($exchange_rate_data['num']) && $exchange_rate_data['num'] > 0 ? $exchange_rate_data['num'] : 0.09;
        } catch (Exception $e) {
            $rub_to_cny_rate = 0.09;
        }
        
        if (!$rub_to_cny_rate || $rub_to_cny_rate <= 0) {
            $rub_to_cny_rate = 0.09;
        }

        // 如果有毛利率筛选，需要先获取所有数据进行筛选，然后再分页
        if ($profit_rate_min !== null || $profit_rate_max !== null) {
            // 先获取所有符合条件的数据用于毛利率筛选
            $all_sql = "SELECT 
                        o.posting_number,
                        o.order_id,
                        o.order_name,
                        o.sku,
                        o.price,
                        o.cost,
                        o.profit,
                        o.quantity,
                        o.status,
                        o.in_process_at,
                        o.delivering_date,
                        o.storeid,
                        o.primary_image,
                        o.delivery AS o_delivery,
                        o.commission_percent AS o_commission_percent,
                        s.storename,
                        p.income as t_income,
                        p.commission as t_commission,
                        p.agency_fee as t_agency_fee,
                        p.delivery_fee as t_delivery_fee,
                        p.acquiring_fee as t_acquiring_fee,
                        p.return_amount as t_return_amount,
                        p.net_profit as t_net_profit,
                        o.price as calculated_profit
                    FROM ozon_order o
                    LEFT JOIN ozon_store s ON o.storeid = s.id
                    LEFT JOIN t_order_profit p ON o.posting_number = p.posting_number
                    WHERE {$where_clause}
                    ORDER BY o.in_process_at DESC";
            
            $all_list = $DB->getAll($all_sql, $params);
            
            // 处理数据并筛选毛利率
            $filtered_list = [];
            foreach ($all_list as &$item) {
                $item['t_income_cny'] = $item['t_income'] ? round($item['t_income'] * $rub_to_cny_rate, 2) : 0;
                $item['t_commission_cny'] = $item['t_commission'] ? round($item['t_commission'] * $rub_to_cny_rate, 2) : 0;
                $item['t_agency_fee_cny'] = $item['t_agency_fee'] ? round($item['t_agency_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_delivery_fee_cny'] = $item['t_delivery_fee'] ? round($item['t_delivery_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_acquiring_fee_cny'] = $item['t_acquiring_fee'] ? round($item['t_acquiring_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_return_amount_cny'] = $item['t_return_amount'] ? round($item['t_return_amount'] * $rub_to_cny_rate, 2) : 0;
                
                // 重新计算利润
                $price = floatval($item['price'] ?? 0);
                $quantity = floatval($item['quantity'] ?? 1);
                $cost = floatval($item['cost'] ?? 0);
                $total_sales = $price * $quantity; // 总销售金额
                $commission_cny = floatval($item['t_commission_cny'] ?? 0);
                $delivery_fee_cny = floatval($item['t_delivery_fee_cny'] ?? 0);
                $agency_fee_cny = floatval($item['t_agency_fee_cny'] ?? 0);
                $acquiring_fee_cny = floatval($item['t_acquiring_fee_cny'] ?? 0);
                $return_amount_cny = floatval($item['t_return_amount_cny'] ?? 0);
                
                // 当状态为"运输中"且财务表暂无数据时，回填订单页的物流费与佣金
                if (($item['status'] ?? '') === 'delivering') {
                    if ((empty($item['t_delivery_fee']) || floatval($item['t_delivery_fee']) == 0) && isset($item['o_delivery']) && $item['o_delivery'] !== null && $item['o_delivery'] !== '') {
                        $delivery_fee_cny = -1 * abs(floatval($item['o_delivery']));
                        $item['t_delivery_fee_cny'] = $delivery_fee_cny;
                    }
                    // 使用新函数计算佣金
                    $calculated_commission = calculateDeliveringCommission($item, $price, $custom_commission_rate, $quantity);
                    if ($calculated_commission != 0) {
                        $commission_cny = $calculated_commission;
                        $item['t_commission_cny'] = $commission_cny;
                    }
                }

                $item['calculated_profit'] = round(($total_sales - $cost + $commission_cny + $delivery_fee_cny + $agency_fee_cny - $acquiring_fee_cny + $return_amount_cny), 2);
                
                // 计算毛利率（基于销售额）
                $profit_rate = $total_sales > 0 ? (($item['calculated_profit'] / $total_sales) * 100) : 0;
                
                // 毛利率筛选
                if ($profit_rate_min !== null && $profit_rate < $profit_rate_min) {
                    continue;
                }
                if ($profit_rate_max !== null && $profit_rate > $profit_rate_max) {
                    continue;
                }
                
                $filtered_list[] = $item;
            }
            
            // 重新计算总数和分页
            $total = count($filtered_list);
            $list = array_slice($filtered_list, $offset, $limit);
            
        } else {
            // 没有毛利率筛选时的正常查询
            $count_sql = "SELECT COUNT(*) FROM ozon_order o 
                         LEFT JOIN t_order_profit p ON o.posting_number = p.posting_number 
                         WHERE {$where_clause}";
            $total = $DB->getColumn($count_sql, $params);
            
            $sql = "SELECT 
                        o.posting_number,
                        o.order_id,
                        o.order_name,
                        o.sku,
                        o.price,
                        o.cost,
                        o.profit,
                        o.quantity,
                        o.status,
                        o.in_process_at,
                        o.delivering_date,
                        o.storeid,
                        o.primary_image,
                        o.delivery AS o_delivery,
                        o.commission_percent AS o_commission_percent,
                        s.storename,
                        p.income as t_income,
                        p.commission as t_commission,
                        p.agency_fee as t_agency_fee,
                        p.delivery_fee as t_delivery_fee,
                        p.acquiring_fee as t_acquiring_fee,
                        p.return_amount as t_return_amount,
                        p.net_profit as t_net_profit,
                        o.price as calculated_profit
                    FROM ozon_order o
                    LEFT JOIN ozon_store s ON o.storeid = s.id
                    LEFT JOIN t_order_profit p ON o.posting_number = p.posting_number
                    WHERE {$where_clause}
                    ORDER BY o.in_process_at DESC
                    LIMIT {$limit} OFFSET {$offset}";
            
            $list = $DB->getAll($sql, $params);
            
            // 处理数据但不进行毛利率筛选
            foreach ($list as &$item) {
                $item['t_income_cny'] = $item['t_income'] ? round($item['t_income'] * $rub_to_cny_rate, 2) : 0;
                $item['t_commission_cny'] = $item['t_commission'] ? round($item['t_commission'] * $rub_to_cny_rate, 2) : 0;
                $item['t_agency_fee_cny'] = $item['t_agency_fee'] ? round($item['t_agency_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_delivery_fee_cny'] = $item['t_delivery_fee'] ? round($item['t_delivery_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_acquiring_fee_cny'] = $item['t_acquiring_fee'] ? round($item['t_acquiring_fee'] * $rub_to_cny_rate, 2) : 0;
                $item['t_return_amount_cny'] = $item['t_return_amount'] ? round($item['t_return_amount'] * $rub_to_cny_rate, 2) : 0;
                
                // 重新计算利润
                $price = floatval($item['price'] ?? 0);
                $quantity = floatval($item['quantity'] ?? 1);
                $cost = floatval($item['cost'] ?? 0);
                $total_sales = $price * $quantity; // 总销售金额
                $commission_cny = floatval($item['t_commission_cny'] ?? 0);
                $delivery_fee_cny = floatval($item['t_delivery_fee_cny'] ?? 0);
                $agency_fee_cny = floatval($item['t_agency_fee_cny'] ?? 0);
                $acquiring_fee_cny = floatval($item['t_acquiring_fee_cny'] ?? 0);
                $return_amount_cny = floatval($item['t_return_amount_cny'] ?? 0);
                
                // 当状态为"运输中"且财务表暂无数据时，回填订单页的物流费与佣金
                if (($item['status'] ?? '') === 'delivering') {
                    if ((empty($item['t_delivery_fee']) || floatval($item['t_delivery_fee']) == 0) && isset($item['o_delivery']) && $item['o_delivery'] !== null && $item['o_delivery'] !== '') {
                        $delivery_fee_cny = -1 * abs(floatval($item['o_delivery']));
                        $item['t_delivery_fee_cny'] = $delivery_fee_cny;
                    }
                    // 使用新函数计算佣金
                    $calculated_commission = calculateDeliveringCommission($item, $price, $custom_commission_rate, $quantity);
                    if ($calculated_commission != 0) {
                        $commission_cny = $calculated_commission;
                        $item['t_commission_cny'] = $commission_cny;
                    }
                }

                $item['calculated_profit'] = round(($total_sales - $cost + $commission_cny + $delivery_fee_cny + $agency_fee_cny - $acquiring_fee_cny + $return_amount_cny), 2);
            }
        }
        
        // 计算统计数据（从已处理的数据中计算）
        $total_orders = count($list);
        $total_sales = 0;
        $total_cost = 0;
        $total_profit = 0;
        $total_net_profit = 0;
        
        foreach ($list as $item) {
            $price = floatval($item['price'] ?? 0);
            $quantity = floatval($item['quantity'] ?? 1);
            $total_sales += $price * $quantity; // 使用总销售金额
            $total_cost += floatval($item['cost'] ?? 0);
            $total_profit += floatval($item['calculated_profit'] ?? 0);
            $total_net_profit += floatval($item['t_net_profit'] ?? 0);
        }
        
        // 如果需要获取所有数据的统计（不仅仅是当前页），重新查询
        if ($total > count($list)) {
            $all_sql = "SELECT 
                            o.price,
                            o.quantity,
                            o.cost,
                            p.commission,
                            p.delivery_fee,
                            p.agency_fee,
                            p.acquiring_fee,
                            p.return_amount,
                            p.net_profit
                        FROM ozon_order o
                        LEFT JOIN t_order_profit p ON o.posting_number = p.posting_number
                        WHERE {$where_clause}";
            
            $all_data = $DB->getAll($all_sql, $params);
            
            $total_orders = count($all_data);
            $total_sales = 0;
            $total_cost = 0;
            $total_profit = 0;
            $total_net_profit = 0;
            
            foreach ($all_data as $row) {
                $price = floatval($row['price'] ?? 0);
                $quantity = floatval($row['quantity'] ?? 1);
                $cost = floatval($row['cost'] ?? 0);
                $total_sales_amount = $price * $quantity; // 总销售金额
                $commission_cny = $row['commission'] ? round($row['commission'] * $rub_to_cny_rate, 2) : 0;
                $delivery_fee_cny = $row['delivery_fee'] ? round($row['delivery_fee'] * $rub_to_cny_rate, 2) : 0;
                $agency_fee_cny = $row['agency_fee'] ? round($row['agency_fee'] * $rub_to_cny_rate, 2) : 0;
                $acquiring_fee_cny = $row['acquiring_fee'] ? round($row['acquiring_fee'] * $rub_to_cny_rate, 2) : 0;
                $return_amount_cny = $row['return_amount'] ? round($row['return_amount'] * $rub_to_cny_rate, 2) : 0;
                
                $total_sales += $total_sales_amount;
                $total_cost += $cost;
                $total_profit += round(($total_sales_amount - $cost + $commission_cny + $delivery_fee_cny + $agency_fee_cny - $acquiring_fee_cny + $return_amount_cny), 2);
                $total_net_profit += floatval($row['net_profit'] ?? 0);
            }
        }
        
        $stats = [
            'total_orders' => $total_orders,
            'total_sales' => $total_sales,
            'total_cost' => $total_cost,
            'total_profit' => $total_profit,
            'total_net_profit' => $total_net_profit
        ];
        
        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'count' => $total,
            'data' => $list,
            'stats' => [
                'total_orders' => (int)$stats['total_orders'],
                'total_sales' => round((float)$stats['total_sales'], 2),
                'total_cost' => round((float)$stats['total_cost'], 2),
                'total_profit' => round((float)$stats['total_profit'], 2),
                'total_net_profit' => round((float)$stats['total_net_profit'], 2)
            ]
        ]));
        break;
        
    case 'get_groups':
        // 获取用户的分组列表
        $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
        $shop_groups = $user && $user['shop_groups'] 
            ? json_decode($user['shop_groups'], true) 
            : ['groups' => [], 'defaultGroupId' => null];
        
        // 获取分组列表
        $groups = $shop_groups['groups'] ?? [];
        
        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => $groups
        ]));
        break;
        
    case 'get_stores':
        // 获取用户的店铺列表
        $sql = "SELECT id, storename FROM ozon_store WHERE uid = ? ORDER BY storename";
        $stores = $DB->getAll($sql, [$uid]);
        
        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => $stores
        ]));
        break;
        
    case 'get_order_statuses':
        // 获取订单状态列表
        $sql = "SELECT DISTINCT status FROM ozon_order WHERE uid = ? AND status IS NOT NULL ORDER BY status";
        $statuses = $DB->getAll($sql, [$uid]);
        
        exit(json_encode([
            'code' => 0,
            'msg' => 'success',
            'data' => $statuses
        ]));
        break;
        
    case 'export_profit':
        // 导出利润数据（兼容 GET/POST 参数）
        $req = $_SERVER['REQUEST_METHOD'] === 'POST' ? $_POST : $_GET;
        // 处理大数据量可能的长时间运行
        @set_time_limit(120);
        
        $start_date = isset($req['start_date']) ? $req['start_date'] : '';
        $end_date = isset($req['end_date']) ? $req['end_date'] : '';
        $delivery_start_date = isset($req['delivery_start_date']) ? $req['delivery_start_date'] : '';
        $delivery_end_date = isset($req['delivery_end_date']) ? $req['delivery_end_date'] : '';
        $status = isset($req['status']) ? $req['status'] : '';
        $group_id = isset($req['group_id']) ? (int)$req['group_id'] : 0;
        $store_id = isset($req['store_id']) ? (int)$req['store_id'] : 0;
        $sku = isset($req['sku']) ? trim($req['sku']) : '';
        $posting_numbers = isset($req['posting_numbers']) ? trim($req['posting_numbers']) : '';
        $profit_rate_min = isset($req['profit_rate_min']) && $req['profit_rate_min'] !== '' ? (float)$req['profit_rate_min'] : null;
        $profit_rate_max = isset($req['profit_rate_max']) && $req['profit_rate_max'] !== '' ? (float)$req['profit_rate_max'] : null;
        $has_refund = isset($req['has_refund']) ? $req['has_refund'] : '';
        $custom_commission_rate = isset($req['custom_commission_rate']) && $req['custom_commission_rate'] !== '' ? (float)$req['custom_commission_rate'] : null;
        
        // 构建查询条件
        $where_conditions = ['o.uid = ?'];
        $params = [$uid];
        
        if (!empty($start_date)) {
            $where_conditions[] = 'DATE(o.in_process_at) >= ?';
            $params[] = $start_date;
        }
        
        if (!empty($end_date)) {
            $where_conditions[] = 'DATE(o.in_process_at) <= ?';
            $params[] = $end_date;
        }
        
        if (!empty($status)) {
            // 支持多个状态：逗号分隔的状态列表
            if (strpos($status, ',') !== false) {
                $status_array = array_filter(array_map('trim', explode(',', $status)));
                if (!empty($status_array)) {
                    $placeholders = str_repeat('?,', count($status_array) - 1) . '?';
                    $where_conditions[] = 'o.status IN (' . $placeholders . ')';
                    $params = array_merge($params, $status_array);
                }
            } else {
                $where_conditions[] = 'o.status = ?';
                $params[] = $status;
            }
        }
        
        if ($store_id > 0) {
            $where_conditions[] = 'o.storeid = ?';
            $params[] = $store_id;
        } elseif ($group_id > 0) {
            // 如果选择了分组，查询该分组下的所有店铺
            $user = $DB->getRow("SELECT shop_groups FROM ozon_user WHERE uid = ?", [$uid]);
            $shop_groups = $user && $user['shop_groups'] 
                ? json_decode($user['shop_groups'], true) 
                : ['groups' => [], 'defaultGroupId' => null];
            
            $shopIds = [];
            foreach ($shop_groups['groups'] ?? [] as $group) {
                if ($group['id'] == $group_id && !empty($group['shopIds'])) {
                    $shopIds = array_map('intval', $group['shopIds']);
                    break;
                }
            }
            
            if (!empty($shopIds)) {
                $placeholders = str_repeat('?,', count($shopIds) - 1) . '?';
                $where_conditions[] = 'o.storeid IN (' . $placeholders . ')';
                $params = array_merge($params, $shopIds);
            } else {
                // 如果分组下没有店铺，返回空结果
                $where_conditions[] = 'o.storeid = -1';
            }
        }
        
        if (!empty($sku)) {
            $where_conditions[] = 'o.sku LIKE ?';
            $params[] = '%' . $sku . '%';
        }
        
        if (!empty($posting_numbers)) {
            // 处理多行订单号输入，兼容 Windows 换行\r\n
            $normalized = str_replace("\r\n", "\n", $posting_numbers);
            $posting_array = array_filter(array_map('trim', explode("\n", $normalized)));
            if (!empty($posting_array)) {
                $placeholders = str_repeat('?,', count($posting_array) - 1) . '?';
                $where_conditions[] = 'o.posting_number IN (' . $placeholders . ')';
                $params = array_merge($params, $posting_array);
            }
        }
        
        if (!empty($delivery_start_date)) {
            $where_conditions[] = 'o.delivering_date >= ?';
            $params[] = $delivery_start_date;
        }
        
        if (!empty($delivery_end_date)) {
            $where_conditions[] = 'o.delivering_date <= ?';
            $params[] = $delivery_end_date;
        }
        
        if ($has_refund === '1') {
            // 有退款：退款金额不为null且不为0（包括负数）
            $where_conditions[] = 'p.return_amount IS NOT NULL AND p.return_amount != 0';
        } elseif ($has_refund === '0') {
            // 无退款：退款金额为null或为0
            $where_conditions[] = '(p.return_amount IS NULL OR p.return_amount = 0.00)';
        }
        
        $where_clause = implode(' AND ', $where_conditions);
        
        $sql = "SELECT 
                    o.posting_number as '订单号',
                    o.order_name as '商品名称',
                    o.sku as 'SKU',
                    s.storename as '店铺名称',
                    o.primary_image as '商品图片',
                    o.price as '单价',
                    o.quantity as '数量',
                    o.cost as '采购成本',
                    p.income as '客户签收金额(₽)',
                    p.commission as '销售佣金(₽)',
                    p.agency_fee as '代理佣金(₽)',
                    p.delivery_fee as '物流费用(₽)',
                    p.acquiring_fee as '支付手续费(₽)',
                    p.return_amount as '退款金额(₽)',
                    o.price as '计算利润',
                    o.price as '成本利润率',
                    o.status as '订单状态',
                    o.delivering_date as '发货时间',
                    o.in_process_at as '下单时间',
                    o.delivery as 'o_delivery',
                    o.commission_percent as 'o_commission_percent'
                FROM ozon_order o
                LEFT JOIN ozon_store s ON o.storeid = s.id
                LEFT JOIN t_order_profit p ON o.posting_number = p.posting_number
                WHERE {$where_clause}
                ORDER BY o.in_process_at DESC";
        
        try {
            $data = $DB->getAll($sql, $params);
        } catch (Throwable $e) {
            exit(json_encode([
                'code' => -1,
                'msg' => '查询失败: ' . $e->getMessage(),
            ]));
        }
        
        // 获取汇率
        try {
            $exchange_rate_data = ForeignExchange(1, '人民币');
            $rub_to_cny_rate = isset($exchange_rate_data['num']) && $exchange_rate_data['num'] > 0 ? $exchange_rate_data['num'] : 0.09;
        } catch (Exception $e) {
            $rub_to_cny_rate = 0.09;
        }
        
        if (!$rub_to_cny_rate || $rub_to_cny_rate <= 0) {
            $rub_to_cny_rate = 0.09;
        }
        
        // 重新计算利润和成本利润率（统一使用人民币，考虑负数情况）
        $filtered_data = [];
        foreach ($data as &$row) {
            $price = floatval($row['单价'] ?? 0);
            $quantity = floatval($row['数量'] ?? 1);
            $cost = floatval($row['采购成本'] ?? 0);
            $total_sales = $price * $quantity; // 总销售金额
            
            // 添加总售价列到导出数据
            $row['总售价'] = $total_sales;
            
            $commission_cny = $row['销售佣金(₽)'] ? round($row['销售佣金(₽)'] * $rub_to_cny_rate, 2) : 0;
            $delivery_fee_cny = $row['物流费用(₽)'] ? round($row['物流费用(₽)'] * $rub_to_cny_rate, 2) : 0;
            $agency_fee_cny = $row['代理佣金(₽)'] ? round($row['代理佣金(₽)'] * $rub_to_cny_rate, 2) : 0;
            $acquiring_fee_cny = $row['支付手续费(₽)'] ? round($row['支付手续费(₽)'] * $rub_to_cny_rate, 2) : 0;
            $return_amount_cny = $row['退款金额(₽)'] ? round($row['退款金额(₽)'] * $rub_to_cny_rate, 2) : 0;
            
            // 添加人民币金额列到导出数据中
            $row['客户签收金额(¥)'] = $row['客户签收金额(₽)'] ? round($row['客户签收金额(₽)'] * $rub_to_cny_rate, 2) : 0;
            $row['销售佣金(¥)'] = $commission_cny;
            $row['代理佣金(¥)'] = $agency_fee_cny;
            $row['物流费用(¥)'] = $delivery_fee_cny;
            $row['支付手续费(¥)'] = $acquiring_fee_cny;
            $row['退款金额(¥)'] = $return_amount_cny;
            
            // 处理运输中状态的自定义佣金和物流费
            if (($row['订单状态'] ?? '') === 'delivering') {
                // 处理物流费回填
                if ((empty($row['物流费用(₽)']) || floatval($row['物流费用(₽)']) == 0) && isset($row['o_delivery']) && $row['o_delivery'] !== null && $row['o_delivery'] !== '') {
                    $delivery_fee_cny = -1 * abs(floatval($row['o_delivery']));
                    $row['物流费用(¥)'] = $delivery_fee_cny;
                }
                
                // 处理佣金回填
                if ((empty($row['销售佣金(₽)']) || floatval($row['销售佣金(₽)']) == 0)) {
                    // 模拟数据结构以使用现有函数
                    $item = [
                        't_commission' => $row['销售佣金(₽)'],
                        'o_commission_percent' => $row['o_commission_percent'],
                        'sku' => $row['SKU']
                    ];
                    $calculated_commission = calculateDeliveringCommission($item, $price, $custom_commission_rate, $quantity);
                    if ($calculated_commission != 0) {
                        $commission_cny = $calculated_commission;
                        $row['销售佣金(¥)'] = $commission_cny; // 更新人民币金额
                    }
                }
            }
            
            // 使用最终的人民币金额来计算利润
            $final_commission_cny = $row['销售佣金(¥)'];
            $final_delivery_fee_cny = $row['物流费用(¥)'];
            
            // 销售佣金、代理佣金、物流费、退款金额为负数（收入），支付手续费有正有负
            // 负数费用需要加到利润中（双重负号变正号），正数费用从利润中减去
            $calculated_profit = round(($total_sales - $cost + $final_commission_cny + $final_delivery_fee_cny + $agency_fee_cny - $acquiring_fee_cny + $return_amount_cny), 2);
            $row['计算利润'] = $calculated_profit;
            
            // 计算利润率（基于销售额）
            $profit_rate = $total_sales > 0 ? (($calculated_profit / $total_sales) * 100) : 0;
            
            // 利润率筛选
            if ($profit_rate_min !== null && $profit_rate < $profit_rate_min) {
                continue;
            }
            if ($profit_rate_max !== null && $profit_rate > $profit_rate_max) {
                continue;
            }
            
            // 计算成本利润率
            if ($cost > 0) {
                $cost_profit_rate = round(($calculated_profit / $cost) * 100, 2);
                $row['成本利润率'] = $cost_profit_rate . '%';
            } else {
                $row['成本利润率'] = '-';
            }
            
            // 移除辅助字段，避免导出到CSV中
            unset($row['o_delivery']);
            unset($row['o_commission_percent']);
            
            $filtered_data[] = $row;
        }
        
        // 使用筛选后的数据
        $data = $filtered_data;
        
        // 生成CSV文件
        $filename = 'profit_report_' . date('Y-m-d_H-i-s') . '.csv';
        $dir = __DIR__ . '/temp_exports/';
        $filepath = $dir . $filename;
        
        // 确保目录存在并可写
        if (!is_dir($dir)) {
            if (!@mkdir($dir, 0775, true) && !is_dir($dir)) {
                if (!@mkdir($dir, 0777, true) && !is_dir($dir)) {
                    exit(json_encode([
                        'code' => -1,
                        'msg' => '创建导出目录失败: ' . $dir,
                    ]));
                }
            }
        }
        if (!is_writable($dir)) {
            @chmod($dir, 0777);
        }
        if (!is_writable($dir)) {
            exit(json_encode([
                'code' => -1,
                'msg' => '导出目录不可写: ' . $dir,
            ]));
        }
        
        $fp = @fopen($filepath, 'w');
        if ($fp === false) {
            exit(json_encode([
                'code' => -1,
                'msg' => '无法创建导出文件',
            ]));
        }
        
        // 写入BOM头，解决中文乱码问题
        fwrite($fp, "\xEF\xBB\xBF");
        
        // 写入表头
        if (!empty($data)) {
            fputcsv($fp, array_keys($data[0]));
            
            // 写入数据
            foreach ($data as $row) {
                fputcsv($fp, $row);
            }
        }
        
        @fclose($fp);
        
        exit(json_encode([
            'code' => 0,
            'msg' => '导出成功',
            'data' => [
                'filename' => $filename,
                'download_url' => './temp_exports/' . $filename
            ]
        ]));
        break;
        
    default:
        exit(json_encode(['code' => -1, 'msg' => '无效的操作']));
}
?>