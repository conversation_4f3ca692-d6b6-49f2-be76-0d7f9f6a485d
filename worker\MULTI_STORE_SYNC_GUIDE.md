# Ozon 多店铺订单同步工具使用说明

## 概述

现在您有三个脚本可以用于Ozon订单同步：

1. `d2.php` - 原始的单店铺同步脚本
2. `d2_multi.php` - 多店铺同步脚本
3. `batch_sync.php` - 批量同步管理工具

## 使用方法

### 1. 多店铺同步脚本 (d2_multi.php)

#### 同步单个店铺
```bash
php d2_multi.php 37 delivered 90
```

#### 同步多个指定店铺
```bash
php d2_multi.php "37,38,39" delivered 90
```

#### 同步所有店铺
```bash
php d2_multi.php all delivered 90
```

#### 开启调试模式
```bash
php d2_multi.php all delivered 90 debug
```

### 2. 批量同步管理工具 (batch_sync.php)

#### 显示帮助
```bash
php batch_sync.php help
```

#### 单店铺模式
```bash
php batch_sync.php single 37 delivered 90
```

#### 多店铺模式
```bash
php batch_sync.php multi "37,38,39" delivered 90
```

#### 所有店铺模式
```bash
php batch_sync.php all delivered 90
```

#### 预设任务模式

**每日同步任务**（最近7天的活跃订单）
```bash
php batch_sync.php preset daily
```

**每周同步任务**（最近30天的所有订单）
```bash
php batch_sync.php preset weekly
```

**每月同步任务**（最近90天的已交付订单）
```bash
php batch_sync.php preset monthly
```

**全状态同步**（最近30天的所有状态订单）
```bash
php batch_sync.php preset all_status
```

#### 查看可用店铺
```bash
php batch_sync.php list-stores
```

## 参数说明

### 订单状态
- `awaiting_packaging` - 待打包
- `awaiting_deliver` - 待发货  
- `delivering` - 配送中
- `delivered` - 已交付
- `all` - 所有状态

### 天数
查询的时间范围，从今天往前推算的天数。

### 调试模式
添加 `debug` 参数可以看到详细的API请求和响应信息。

## 输出文件

同步完成后，数据会保存在以下位置：

### 单店铺文件
```
worker/data/{店铺ID}_{状态}_{天数}days.json
```

### 合并文件（多店铺时）
```
worker/data/all_stores_{状态}_{天数}days.json
```

## 使用建议

### 日常使用
```bash
# 每日同步活跃订单
php batch_sync.php preset daily

# 手动同步特定店铺的最新订单
php d2_multi.php 37 awaiting_packaging 7
```

### 定期任务
可以设置定时任务，例如：

```bash
# 每天早上8点同步活跃订单
0 8 * * * cd /path/to/worker && php batch_sync.php preset daily

# 每周一同步全部订单
0 9 * * 1 cd /path/to/worker && php batch_sync.php preset weekly

# 每月1号同步历史订单
0 10 1 * * cd /path/to/worker && php batch_sync.php preset monthly
```

### 故障排查
如果遇到问题，可以：

1. 使用调试模式查看详细信息
2. 检查店铺配置是否正确
3. 确认API密钥是否有效
4. 查看网络连接是否正常

## 高级功能

### 自定义预设任务
您可以修改 `batch_sync.php` 中的 `getPresetTasks()` 方法来添加自定义的预设任务。

### API限流处理
脚本已内置API限流处理：
- 每个API请求后延迟0.5秒
- 店铺间延迟1秒
- 预设任务间延迟2秒

### 错误恢复
- 单个店铺失败不会影响其他店铺的同步
- 详细的错误日志帮助定位问题
- 支持重试机制

## 示例场景

### 场景1：紧急订单查询
```bash
# 查询店铺37最近1天的待打包订单
php d2_multi.php 37 awaiting_packaging 1 debug
```

### 场景2：月末报表数据
```bash
# 获取所有店铺最近30天的已交付订单
php d2_multi.php all delivered 30
```

### 场景3：定期维护
```bash
# 执行完整的数据同步
php batch_sync.php preset all_status
```