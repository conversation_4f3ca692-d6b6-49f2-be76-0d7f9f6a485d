<?php
/**
 * 简化版批量订单状态更新脚本
 * 基于现有的 update_order_status.php，但使用批量SQL操作
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

class SimpleBatchUpdater {
    private $DB;
    private $debug;
    
    public function __construct($debug = false) {
        global $DB;
        $this->DB = $DB;
        $this->debug = $debug;
        
        if (!$this->DB) {
            echo "错误: 数据库连接失败\n";
            exit(1);
        }
    }
    
    /**
     * 读取JSON文件
     */
    public function readJsonFile($filename) {
        $filepath = __DIR__ . '/data/' . $filename;
        
        if (!file_exists($filepath)) {
            echo "错误: 文件不存在 - {$filename}\n";
            return false;
        }
        
        $content = file_get_contents($filepath);
        $data = json_decode($content, true);
        
        if ($data === null) {
            echo "错误: JSON格式无效 - {$filename}\n";
            return false;
        }
        
        return $data;
    }
    
    /**
     * 批量更新订单状态（简化版）
     */
    public function batchUpdate($orders, $filename = '') {
        echo "处理文件: {$filename}\n";
        
        if (empty($orders)) {
            echo "  文件为空，跳过\n\n";
            return;
        }
        
        $totalCount = count($orders);
        echo "  订单总数: {$totalCount}\n";
        
        $stats = [
            'processed' => 0,
            'updated' => 0,
            'not_found' => 0,
            'no_change' => 0,
            'errors' => 0
        ];
        
        // 分批处理，每批100个
        $batchSize = 100;
        $batches = array_chunk($orders, $batchSize);
        
        echo "  分 " . count($batches) . " 批处理\n";
        
        foreach ($batches as $batchIndex => $batch) {
            echo "    批次 " . ($batchIndex + 1) . "/" . count($batches) . ": " . count($batch) . " 个订单\n";
            
            foreach ($batch as $order) {
                if (!isset($order['posting_number']) || !isset($order['status'])) {
                    $stats['errors']++;
                    continue;
                }
                
                $postingNumber = $order['posting_number'];
                $newStatus = $order['status'];
                $stats['processed']++;
                
                try {
                    // 查询当前状态 - 使用 order 表而不是 ozon_order 表
                    $existingOrder = $this->DB->find('order', ['status', 'storeid'], ['posting_number' => $postingNumber]);
                    
                    if (!$existingOrder) {
                        $stats['not_found']++;
                        if ($this->debug) {
                            echo "      ✗ {$postingNumber}: 订单不存在\n";
                            
                            // 尝试模糊查询，看看是否有相似的订单号
                            $similarOrders = $this->DB->getAll(
                                "SELECT posting_number FROM order WHERE posting_number LIKE ? LIMIT 3", 
                                ['%' . substr($postingNumber, -6) . '%']
                            );
                            if ($similarOrders) {
                                echo "        相似订单: ";
                                foreach ($similarOrders as $similar) {
                                    echo $similar['posting_number'] . " ";
                                }
                                echo "\n";
                            }
                        }
                        continue;
                    }
                    
                    $currentStatus = $existingOrder['status'];
                    
                    if ($currentStatus === $newStatus) {
                        $stats['no_change']++;
                        continue;
                    }
                    
                    // 更新状态 - 使用 order 表
                    $result = $this->DB->update('order', 
                        ['status' => $newStatus], 
                        ['posting_number' => $postingNumber]
                    );
                    
                    if ($result) {
                        $stats['updated']++;
                        if ($this->debug) {
                            echo "      ✓ {$postingNumber}: {$currentStatus} → {$newStatus}\n";
                        }
                    } else {
                        $stats['errors']++;
                        if ($this->debug) {
                            echo "      ✗ {$postingNumber}: 更新失败\n";
                        }
                    }
                    
                } catch (Exception $e) {
                    $stats['errors']++;
                    if ($this->debug) {
                        echo "      ✗ {$postingNumber}: " . $e->getMessage() . "\n";
                    }
                }
            }
            
            // 显示批次进度
            $progress = round(($batchIndex + 1) / count($batches) * 100, 1);
            echo "    进度: {$progress}%\n";
        }
        
        // 显示统计
        echo "  处理完成:\n";
        echo "    总订单数: {$stats['processed']}\n";
        echo "    更新成功: {$stats['updated']}\n";
        echo "    无需更新: {$stats['no_change']}\n";
        echo "    订单不存在: {$stats['not_found']}\n";
        echo "    错误: {$stats['errors']}\n\n";
        
        return $stats;
    }
    
    /**
     * 处理单个文件
     */
    public function processFile($filename) {
        echo "=== 处理文件: {$filename} ===\n";
        $startTime = microtime(true);
        
        $orders = $this->readJsonFile($filename);
        if ($orders === false) {
            return false;
        }
        
        $stats = $this->batchUpdate($orders, $filename);
        
        $endTime = microtime(true);
        $duration = round($endTime - $startTime, 2);
        
        echo "文件处理完成!\n";
        echo "耗时: {$duration} 秒\n";
        if ($stats['processed'] > 0) {
            echo "速度: " . round($stats['processed'] / $duration, 2) . " 订单/秒\n";
        }
        echo "\n";
        
        return $stats;
    }
    
    /**
     * 诊断数据匹配问题
     */
    public function diagnoseFile($filename) {
        echo "=== 诊断文件: {$filename} ===\n";
        
        $orders = $this->readJsonFile($filename);
        if ($orders === false) {
            return false;
        }
        
        $totalCount = count($orders);
        echo "订单总数: {$totalCount}\n";
        
        // 取前10个订单进行详细诊断
        $sampleOrders = array_slice($orders, 0, 10);
        echo "诊断样本: 前" . count($sampleOrders) . "个订单\n\n";
        
        foreach ($sampleOrders as $index => $order) {
            echo "[订单 " . ($index + 1) . "]\n";
            
            if (!isset($order['posting_number'])) {
                echo "  ✗ 缺少 posting_number 字段\n";
                continue;
            }
            
            $postingNumber = $order['posting_number'];
            echo "  订单号: {$postingNumber}\n";
            echo "  JSON状态: " . ($order['status'] ?? '未设置') . "\n";
            echo "  JSON店铺ID: " . ($order['store_id'] ?? '未设置') . "\n";
            
            // 精确查询 - 使用 order 表
            $exactMatch = $this->DB->find('order', 
                ['posting_number', 'status', 'storeid', 'uid'], 
                ['posting_number' => $postingNumber]
            );
            
            if ($exactMatch) {
                echo "  ✓ 数据库中找到\n";
                echo "    DB状态: {$exactMatch['status']}\n";
                echo "    DB店铺ID: {$exactMatch['storeid']}\n";
                echo "    DB用户ID: {$exactMatch['uid']}\n";
                
                if ($exactMatch['status'] === ($order['status'] ?? '')) {
                    echo "    状态匹配: ✓\n";
                } else {
                    echo "    状态不匹配: 需要更新\n";
                }
            } else {
                echo "  ✗ 数据库中未找到\n";
                
                // 模糊查询 - 使用 order 表
                $similarOrders = $this->DB->getAll(
                    "SELECT posting_number, status, storeid FROM order WHERE posting_number LIKE ? LIMIT 3", 
                    ['%' . substr($postingNumber, -6) . '%']
                );
                
                if ($similarOrders) {
                    echo "    相似订单:";
                    foreach ($similarOrders as $similar) {
                        echo " {$similar['posting_number']}";
                    }
                    echo "\n";
                } else {
                    echo "    未找到相似订单\n";
                }
            }
            
            echo "\n";
        }
        
        // 统计所有订单的匹配情况
        echo "=== 全量统计 ===\n";
        
        $postingNumbers = array_column($orders, 'posting_number');
        $postingNumbers = array_filter($postingNumbers); // 过滤空值
        
        if (empty($postingNumbers)) {
            echo "错误: 没有有效的订单号\n";
            return false;
        }
        
        echo "有效订单号数量: " . count($postingNumbers) . "\n";
        
        // 批量查询数据库中存在的订单
        $chunks = array_chunk($postingNumbers, 100);
        $foundCount = 0;
        
        foreach ($chunks as $chunk) {
            $placeholders = str_repeat('?,', count($chunk) - 1) . '?';
            $existingOrders = $this->DB->getAll(
                "SELECT posting_number FROM order WHERE posting_number IN ({$placeholders})",
                $chunk
            );
            $foundCount += count($existingOrders);
        }
        
        echo "数据库中存在: {$foundCount} 个\n";
        echo "数据库中不存在: " . (count($postingNumbers) - $foundCount) . " 个\n";
        echo "匹配率: " . round($foundCount / count($postingNumbers) * 100, 2) . "%\n";
        
        return true;
    }
    public function processAll() {
        echo "=== 批量处理所有JSON文件 ===\n";
        
        $dataDir = __DIR__ . '/data';
        $files = glob($dataDir . '/*.json');
        
        // 过滤掉合并文件
        $files = array_filter($files, function($file) {
            return !str_contains(basename($file), 'all_stores_');
        });
        
        if (empty($files)) {
            echo "没有找到JSON文件\n";
            return false;
        }
        
        echo "找到 " . count($files) . " 个文件\n\n";
        
        $totalStats = [
            'processed' => 0,
            'updated' => 0,
            'not_found' => 0,
            'no_change' => 0,
            'errors' => 0
        ];
        
        $totalStartTime = microtime(true);
        
        foreach ($files as $filepath) {
            $filename = basename($filepath);
            $orders = $this->readJsonFile($filename);
            
            if ($orders !== false) {
                $stats = $this->batchUpdate($orders, $filename);
                
                foreach ($totalStats as $key => $value) {
                    $totalStats[$key] += $stats[$key];
                }
            }
        }
        
        $totalEndTime = microtime(true);
        $totalDuration = round($totalEndTime - $totalStartTime, 2);
        
        echo "所有文件处理完成!\n";
        echo "总更新订单: {$totalStats['updated']} 个\n";
        echo "总耗时: {$totalDuration} 秒\n";
        if ($totalStats['processed'] > 0) {
            echo "平均速度: " . round($totalStats['processed'] / $totalDuration, 2) . " 订单/秒\n";
        }
        
        return $totalStats;
    }
}

// 显示帮助
function showHelp() {
    echo "=== 简化版批量订单状态更新工具 ===\n\n";
    echo "使用方法:\n";
    echo "php simple_batch_update.php [模式] [参数]\n\n";
    echo "模式:\n";
    echo "  file [文件名] [debug] - 更新单个文件\n";
    echo "  all [debug]          - 更新所有文件\n";
    echo "  diagnose [文件名]    - 诊断文件中的订单匹配问题\n";
    echo "  help                 - 显示帮助\n\n";
    echo "示例:\n";
    echo "  php simple_batch_update.php file 139_delivered_90days.json\n";
    echo "  php simple_batch_update.php all debug\n";
    echo "  php simple_batch_update.php diagnose 139_delivered_90days.json\n\n";
}

// 主程序
if ($argc < 2) {
    showHelp();
    exit(1);
}

$mode = $argv[1];
$debug = in_array('debug', $argv);

echo "数据库状态: " . (isset($DB) && $DB ? "已连接" : "未连接") . "\n\n";

$updater = new SimpleBatchUpdater($debug);

switch ($mode) {
    case 'help':
        showHelp();
        break;
        
    case 'file':
        $filename = $argv[2] ?? '';
        if (empty($filename)) {
            echo "错误: 请提供文件名\n";
            showHelp();
            exit(1);
        }
        $updater->processFile($filename);
        break;
        
    case 'all':
        $updater->processAll();
        break;
        
    case 'diagnose':
        $filename = $argv[2] ?? '';
        if (empty($filename)) {
            echo "错误: 请提供文件名\n";
            showHelp();
            exit(1);
        }
        $updater->diagnoseFile($filename);
        break;
        
    default:
        echo "错误: 未知的模式 '{$mode}'\n\n";
        showHelp();
        exit(1);
}