<?php
/**
 * 自定义店铺列表同步脚本
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

// 您的自定义店铺ID列表
$customStoreIds = [
    31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
    41, 42, 43, 44, 45, 46, 47, 48, 49, 50,
    52, 53, 54, 55, 56, 57, 58, 61, 62, 63,
    64, 66, 67, 68, 69, 70, 71, 72, 73, 74,
    75, 76, 77, 78, 79, 80, 81, 95, 139, 140,
    142, 143, 144, 145, 146, 147, 148, 149, 150, 151,
    152, 154, 155, 156, 157, 159, 160, 161, 162, 210,
    211, 212, 213, 214, 215, 216, 217, 218, 219, 221,
    222, 223, 224, 225, 226, 227, 228, 229, 230, 231,
    232, 233, 234, 235, 236, 237, 238, 239, 240, 241,
    242, 243, 258, 260, 311, 322, 362, 363, 366, 419,
    750, 751, 752, 756, 759
];

// 从命令行参数获取配置，或使用默认值
$status = $argv[1] ?? 'delivered';  // 订单状态
$days = $argv[2] ?? 90;  // 查询天数
$debug = isset($argv[3]) && $argv[3] === 'debug'; // 调试模式

echo "=== 自定义店铺列表同步脚本 ===\n";
echo "要同步的店铺数量: " . count($customStoreIds) . "\n";
echo "店铺ID列表: " . implode(', ', array_slice($customStoreIds, 0, 10)) . "...\n";
echo "订单状态: {$status}\n";
echo "查询天数: {$days}\n";
echo "调试模式: " . ($debug ? '开启' : '关闭') . "\n\n";

// 验证店铺是否存在
echo "正在验证店铺...\n";
$validStores = [];
$invalidStores = [];

foreach ($customStoreIds as $storeId) {
    $store = $DB->find('store', ['id', 'ClientId', 'status'], ['id' => $storeId]);
    if ($store) {
        $validStores[] = $storeId;
        echo "✓ 店铺 {$storeId} - {$store['ClientId']}\n";
    } else {
        $invalidStores[] = $storeId;
        echo "✗ 店铺 {$storeId} - 不存在\n";
    }
}

echo "\n验证结果:\n";
echo "有效店铺: " . count($validStores) . " 个\n";
echo "无效店铺: " . count($invalidStores) . " 个\n";

if (!empty($invalidStores)) {
    echo "无效店铺ID: " . implode(', ', $invalidStores) . "\n";
}

if (empty($validStores)) {
    echo "\n错误: 没有有效的店铺可以同步!\n";
    exit(1);
}

echo "\n" . str_repeat('=', 60) . "\n";
echo "开始同步 " . count($validStores) . " 个有效店铺...\n";

// 调用多店铺同步脚本
$storeParam = implode(',', $validStores);
$command = "php " . __DIR__ . "/d2_multi.php";

$args = [
    escapeshellarg($storeParam),
    escapeshellarg($status),
    escapeshellarg($days)
];

if ($debug) {
    $args[] = 'debug';
}

$fullCommand = $command . ' ' . implode(' ', $args);

echo "执行命令: {$fullCommand}\n";
echo str_repeat('-', 60) . "\n";

// 执行同步命令
$startTime = microtime(true);
$handle = popen($fullCommand . ' 2>&1', 'r');

if ($handle) {
    while (!feof($handle)) {
        $line = fgets($handle);
        if ($line !== false) {
            echo $line;
        }
    }
    $exitCode = pclose($handle);
    
    $endTime = microtime(true);
    $duration = round($endTime - $startTime, 2);
    
    echo str_repeat('=', 60) . "\n";
    echo "同步完成!\n";
    echo "执行时间: {$duration} 秒\n";
    echo "退出码: {$exitCode}\n";
    
    if ($exitCode === 0) {
        echo "✅ 所有店铺同步成功!\n";
        
        // 显示数据文件位置
        echo "\n数据文件位置:\n";
        echo "- 各店铺数据: worker/data/{店铺ID}_{$status}_{$days}days.json\n";
        echo "- 合并数据: worker/data/all_stores_{$status}_{$days}days.json\n";
    } else {
        echo "❌ 部分或全部店铺同步失败!\n";
    }
} else {
    echo "❌ 无法执行同步命令!\n";
    exit(1);
}

// 生成统计报告
echo "\n" . str_repeat('=', 60) . "\n";
echo "统计报告\n";
echo "请求同步店铺: " . count($customStoreIds) . " 个\n";
echo "有效店铺: " . count($validStores) . " 个\n";
echo "无效店铺: " . count($invalidStores) . " 个\n";
echo "订单状态: {$status}\n";
echo "查询范围: 最近 {$days} 天\n";