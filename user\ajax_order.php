<?php
// 开启所有错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);


// 设置自定义错误处理函数
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    error_log("PHP Error [$errno] $errstr in $errfile on line $errline");
    throw new ErrorException($errstr, 0, $errno, $errfile, $errline);
});

// 设置异常处理函数
set_exception_handler(function($e) {
    error_log("Uncaught Exception: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
    http_response_code(500);
    die(json_encode([
        'code' => 500,
        'message' => 'Internal Server Error',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTrace()
    ]));
});


include("../includes/common.php");
use setasign\Fpdi\Tcpdf\Fpdi;
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;
require_once('../includes/vendor/tecnickcom/tcpdf/tcpdf.php');
$act = isset($_GET['act']) ? daddslashes($_GET['act']) : null;



if (!checkRefererHost())
    exit('{"code":403}');
if ($act != 'login' and $act != 'reg') {
    if ($islogin2 == 1) {
    } else
        exit('{"code":-3,"msg":"No Login"}');
}
@header('Content-Type: application/json; charset=UTF-8');


switch ($act) {
case 'order_status_counts':
    // 获取各个订单状态的统计数量
    $counts = [];

    // 待处理 (所有未完成的订单：未采购+已采购但未发货)
    $counts['pending'] =  $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'cancelled_from_split_pending')", [$uid]);

    // 未采购 (成本为空且未取消、未送达、未运输、未发货)
    $counts['not_purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'cancelled_from_split_pending', 'awaiting_verification')", [$uid]);

    // 已采购 (有成本但还未进入发货流程的订单)
    $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND cost IS NOT NULL AND cost != ''  AND status NOT IN ('cancelled', 'delivered', 'delivering',  'awaiting_deliver2','awaiting_deliver' ,'cancelled_from_split_pending', 'awaiting_registration')", [$uid]);
    
    // 未上传护照
    $counts['awaiting_verification'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_verification'", [$uid]);
    
   /* $counts['purchased'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND cost IS NOT NULL AND cost != '' AND status = 'awaiting_packaging' AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'awaiting_deliver2', 'awaiting_registration')", [$uid]);*/
    
    
/*
    // 移交给快递
    $counts['awaiting_registration'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_registration'", [$uid]);

    // 未发货 (已采购但还未申请备货的订单，即awaiting_packaging状态)
    $counts['not_shipped'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (status = 'awaiting_packaging' OR status = 'awaiting_registration')", [$uid]);
*/
    // 等待发货
    $counts['awaiting_deliver'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_deliver' AND packing_status = 0 AND cost!= 'NULL'", [$uid]);

    // 交运平台
    $counts['awaiting_deliver2'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'awaiting_deliver' AND packing_status = 1", [$uid]);

    // 运输中
    $counts['delivering'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'delivering'", [$uid]);

    // 已送达
    $counts['delivered'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'delivered'", [$uid]);

    // 已取消
    $counts['cancelled'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND status = 'cancelled'", [$uid]);

    // 全部订单 (除已取消)
    $counts['all'] = $DB->getColumn("SELECT COUNT(*) FROM ozon_order WHERE uid = ? AND (status!='cancelled' OR status!='cancelled_from_split_pending')", [$uid]);

    exit(json_encode(['code' => 0, 'data' => $counts]));
break;

case 'orders_all':
    $status = trim($_GET['status']);
    $text = trim($_GET['text']);
    $money = trim($_GET['money']);
    $moneys = trim($_GET['moneys']);
    $storeid = trim($_GET['storeid']);
    $date1 = trim($_GET['date1']);
    $date2 = trim($_GET['date2']);
    $groupid = trim($_GET['groupid']); // 获取分组ID
    $paypt = trim($_GET['paypt']);
    $courierFilter = trim($_GET['courierFilter']);
    
    $sql = " uid={$uid}";
    // 分组筛选逻辑
    if ($groupid && $groupid !== '') {
        $shopGroups = json_decode($userrow['shop_groups'], true);
        $groupShopIds = [];
        foreach ($shopGroups['groups'] as $group) {
            if ($group['id'] == $groupid) {
                $groupShopIds = $group['shopIds'];
                break;
            }
        }
        if (!empty($groupShopIds)) {
            $shopIdsStr = implode(',', $groupShopIds);
        }
        $sql .= " AND storeid IN ({$shopIdsStr})";
    }
    if ($status) {
        if ($status == 'pending') {
            // 待处理：所有未完成的订单
           $sql .= " AND status NOT IN ('cancelled', 'delivered', 'delivering', 'cancelled_from_split_pending')";
        } else if ($status == 'not_purchased') {
            // 未采购：成本为空且未完成
            $sql .= " AND (cost IS NULL OR cost = '') AND status NOT IN ('cancelled', 'delivered', 'delivering', 'cancelled_from_split_pending', 'awaiting_verification')";
        } else if ($status == 'purchased') {
            // 已采购：有成本但还未进入发货流程的订单
            $sql .= " AND cost IS NOT NULL AND cost != '' AND status NOT IN ('cancelled', 'delivered', 'delivering', 'awaiting_deliver', 'cancelled_from_split_pending','awaiting_deliver','awaiting_registration')";
        } else if ($status == 'not_shipped') {
            // 未发货：已采购但还未申请备货的订单
            $sql .= " AND status = 'awaiting_packaging'";
        } else if ($status == 'all') {
            $sql .= " AND (status!='cancelled' OR status!='cancelled_from_split_pending')";
        } else if ($status == 'awaiting_deliver') {
            //等待发货状态：必须是awaiting_deliver状态、打包状态为0(未交运)且已有采购成本(已完成采购)
            //$sql .= " AND (status='awaiting_deliver' OR status='awaiting_registration') AND packing_status=0";
             $sql .= " AND (status='awaiting_deliver' OR status='awaiting_registration') AND packing_status=0 AND cost IS NOT NULL AND cost != ''";
        } else if ($status == 'awaiting_deliver2') {
            $sql .= " AND status='awaiting_deliver' AND packing_status=1";
        } else if($status == 'cancelled'){
            $sql .= " AND status IN ('cancelled','cancelled_from_split_pending')";
        } else {
            $sql .= " AND status='{$status}'";
        }
    }
    if($paypt){
        if($paypt=='pdd'){
            $sql .= " AND purchase_type='pdd'";
        }else{
            $sql .= " AND purchase_type='1688'";
        }
    }
    if ($_GET['cancelled']=='yes' and $status !== 'cancelled'){
        $sql .= " AND status NOT IN ('cancelled', 'cancelled_from_split_pending', 'awaiting_verification')";
    }
    if ($text) {
       /* $sql .= " AND (order_name LIKE '%{$text}%' OR name2 LIKE '%{$text}%' OR posting_number='{$text}' OR sku='{$text}' OR purchase_orderSn='{$text}' OR courierNumber LIKE '%{$text}%' OR tracking_number='{$text}')";*/
           // 处理批量订单号搜索和常规搜索
        $text = trim($text);
        
        // 检查是否包含换行符，判断是否为批量订单号搜索
        if (strpos($text, "\n") !== false || strpos($text, "\r") !== false) {
            // 批量订单号搜索模式
            $normalized = str_replace(["\r\n", "\r"], "\n", $text);
            $posting_array = array_filter(array_map('trim', explode("\n", $normalized)));
            if (!empty($posting_array)) {
                // 限制批量搜索数量，避免性能问题
                if (count($posting_array) > 500) {
                    $posting_array = array_slice($posting_array, 0, 500);
                }
                // 使用更安全的方式构建 IN 条件
                $escaped_postings = array();
                foreach ($posting_array as $posting) {
                    $escaped_postings[] = "'" . daddslashes($posting) . "'";
                }
                $posting_list = implode(',', $escaped_postings);
                $sql .= " AND posting_number IN ({$posting_list})";
            }
        } else {
            // 常规搜索模式（单个关键词）
            $sql .= " AND (order_name LIKE '%{$text}%' OR name2 LIKE '%{$text}%' OR posting_number='{$text}' OR sku='{$text}' OR offer_id='{$text}' OR purchase_orderSn='{$text}' OR courierNumber LIKE '%{$text}%' OR tracking_number='{$text}')";
        }
    }
    if ($storeid) {
        $sql .= " AND storeid='{$storeid}'";
    }
    if ($money || $moneys) {
        if ($money === '')
            $money = 0;
        if ($moneys === '')
            $moneys = 999999999;
        $sql .= " AND price >= '{$money}' AND price <= '{$moneys}'";
    }
    if ($date1 and $date2) {
        $sql .= " AND date>='{$date1}' AND date<= '{$date2}'";
    }
    if ($courierFilter=='empty'){
        $sql.=" AND courierNumber IS NULL";
    }
    if ($courierFilter=='not_empty'){
        $sql.=" AND courierNumber IS NOT NULL";
    }
    $page = intval($_GET['page']);
    $limit = intval($_GET['limit']);
    $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
    $offset = $limit * ($page - 1);
    
    // 先获取总记录数
    $total = $DB->getColumn("SELECT count(*) from ozon_order WHERE{$sql}");
    
    $sort_field = isset($_GET['sort_field']) ? $_GET['sort_field'] : 'in_process_at';
    $sort_order = isset($_GET['sort_order']) ? $_GET['sort_order'] : 'desc';
    $orderBy = "  ORDER BY {$sort_field} {$sort_order} ";
    
    $finalSql = "SELECT * FROM ozon_order WHERE{$sql}";
    //exit($finalSql);
    if (in_array($status, ['pending', 'not_purchased', 'purchased'])) {
        // 对于待处理相关的标签，使用优化函数
        $finalSql = optimize_pending_query($finalSql, $status);
    } else {
        // 其他标签使用常规排序
        $finalSql .= $orderBy;
    }
    
    $finalSql .= " limit $offset,$limit";
    
    $list = $DB->getAll($finalSql);
    
    $list2 = [];
    foreach ($list as $row) {
        $store = getRedis('store');
        if($store and isset($store[$row['storeid']]['storename'])){
            $row['storename'] = $store[$row['storeid']]['storename'];
        }else{
            $row['storename'] = "店铺已删除不存在";
        }
        $Specification = formatSpecification($row);
        $row['pj'] = $Specification[1];
        
        if(empty($row['weight'])){
            $row['weight'] = $Specification[0];
        }
        // 检查是否有多个商品
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        $row['money'] = $row['price'];
        // 如果佣金百分比为空，尝试从Redis缓存获取
        if(empty($row['commission_percent']) || $row['commission_percent'] == 0){
            $product_commission_task = getRedis($row['sku']."_commission");
            if($product_commission_task && $product_commission_task > 0){
                $row['commission_percent'] = $product_commission_task;
                $DB->update('order', ['commission_percent' => $product_commission_task], ['posting_number' => $row['posting_number']]);
            }
        }
        
        // 处理佣金数据，确保显示的完整性
        $commission_percent = floatval($row['commission_percent'] ?? 0);
        $row['percent'] = $commission_percent;
        
        if($commission_percent > 0) {
            $row['commissions'] = round($row['money'] * ($commission_percent / 100), 2) . " ￥";
        } else {
            $row['commissions'] = "待获取";
            // 记录需要获取佣金的商品，用于后续异步处理
            if(!empty($row['sku'])) {
                error_log("Commission missing for SKU: " . $row['sku'] . ", posting_number: " . $row['posting_number']);
            }
        }
        
        $formatted = preg_replace('/^\d{4}-/', '', $row['in_process_at']);
        $row['in_process_at'] = substr($formatted, 0, 11); // 取前11个字符（08-02 17:00）
        $shipmentDate = new DateTime($row['shipment_date']);
        $row['shipment_date'] = substr(preg_replace('/^\d{4}-/', '', $row['shipment_date']), 0, 11);
        
        $shipmentDate->modify('+7 days');
        $row['out_date'] = preg_replace('/^\d{4}-/', '', $shipmentDate->format('Y-m-d H:i'));
        
           
          // 检查是否有多个商品
        $has_multiple_products = !empty($row['products']) && $products && count($products) > 1;
        if ($has_multiple_products) {
            
        } else {
            $row['price'] = round($row['price'] * $row['quantity'], 2);
        }
        
    
        $row['weight'] = round(($row['weight'] / 1000) * $row['quantity'], 2);
        
        $process = processShippingInfo($row);
        $row['wl'] = $process['wl'];
        $row['speed'] = $process['speed'];
        $speedtx = $process['speedtx'];
        if(!isOversizeOrHkShipping($process['exp2'])){
            unset($row['bubble']);
        }
        $row['provider'] = ozonmethodtype($row['tpl_provider']);
        
        
        if ($row['out_weight']) {
            $weight_type = 0;
            $row['out_weight'] = round($row['out_weight'] / 1000, 2);
            $weight = $row['out_weight'];
        } else {
            $weight_type = 1;
            $row['out_weight'] = "未出库";
            $weight = $row['weight'];
        }
        
        if(empty($row['delivery']))$row['delivery'] = calculateShippingCost(findShippingMethod(['provider' => $row['wl'],'method' => $row['provider'],'speed' => $process['speedtx']]), $weight);
        if(empty($row['profit']))$row['profit'] = calculateResult($row['price'], $weight, $row['commission_percent'], $row['delivery'], $row['cost']);
        if($row['cost']>=0.01){
            $zmoney = round((float)$row['cost']-(float)$row['delivery']-(float)$row['commissions']*100,2);
            $row['costprofit'] = round(($row['profit']/$row['cost'])*100,2);
        }
        /*
        if ($weight_type == 0) {
            $DB->update('order', ['delivery' => $row['delivery'], 'profit' => $row['profit']], ['order_id' => $row['order_id']]);
        }
        */
        $row['customer_name'] = null;
        if ($row['customer']) {
            $customer = json_decode($row['customer'], true);
            $row['customer_name'] = $customer['name'];
            $row['region'] = $customer['address']['region'] . ($customer['address']['zip_code'] ? " / " . $customer['address']['zip_code'] : '');
            if ($customer['address']['country'] == 'Rossiya') {
                $row['country'] = "俄罗斯";
            }
        }
        
        if($row['customer_name']){$row['is_blacklisted'] = isCustomerBlacklisted($row['customer_name']);}
        
        // 添加多商品信息
        if (!empty($products) && is_array($products) && count($products) > 1) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
            
            // 为每个商品添加图片信息
            $images = [];
            if(strpos($row['primary_image'], ',')){
                $images = explode(',',$row['primary_image']);
                
                foreach ($row['products_info'] as $key => $product) {
                    $row['products_info'][$key]['image'] = $images[$key];
                }
            }else{
                sendAMQPMessage('product_images_task',$row);
                foreach ($row['products_info'] as $key => $product) {
                    $row['products_info'][$key]['image'] = '../assets/img/syncing.png';
                }
                if(isset($images)){
                    $DB->update('order', ['primary_image' => implode(',', $images)], ['posting_number' => $row['posting_number']]);
                }
            }
        } else {
            $row['has_multiple_products'] = false;
        }
        $AMQPMessagetype = null;
        // 检查是否需要异步获取商品信息
        $needsCommission = empty($row['commission_percent']) || $row['commission_percent'] == 0;
        $needsImage = empty($row['primary_image']);
        
        if ($needsImage && $needsCommission) {
            $AMQPMessagetype = 'product_full_task';
        } elseif ($needsCommission) {
            $AMQPMessagetype = 'product_commission_task';
            // 记录佣金获取需求
            error_log("Scheduling commission task for SKU: " . $row['sku'] . ", posting_number: " . $row['posting_number']);
        } elseif ($needsImage) {
            $AMQPMessagetype = 'product_info_task';
        } elseif (empty($row['order_name'])){
            $AMQPMessagetype = 'product_Translated_title';
        }
        if($AMQPMessagetype){
            $row['AMQPMessagetype'] = $AMQPMessagetype;
            if(sendAMQPMessage($AMQPMessagetype,$row)){
                $row['AMQPMessagestatus'] = true;
            }else{
                $row['AMQPMessagestatus'] = false;
            }
        }
        $row['time'] = date("Y-m-d H:i:s",$row['time']);
        if($row['primary_image'])$row['primary_image'] = str_replace('https://cdn1.ozone.ru/', 'https://ir-2.ozonstatic.cn/', $row['primary_image']);
        unset($row['uid']);
        unset($row['customer']);
        unset($row['Outbound_data']);
        unset($row['stocks']);
        unset($row['products']);
        unset($row['purchase_id']);
        unset($row['material']);
        unset($row['size']);
        unset($row['specification']);
        unset($row['previous_substatus']);
        unset($row['dimensions']);
        $list2[] = $row;
    }
    flush(); // 确保输出发送到浏览器
    echo(json_encode(['code' => 0, 'count' => $total, 'data' => $list2]));
break;
case 'batch_ship': #批量移入交运平台
    $posting_numbers = isset($_POST['posting_numbers']) ? $_POST['posting_numbers'] : [];
    if (!is_array($posting_numbers) || empty($posting_numbers)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要交运的订单']));
    }
    $uid = $islogin2 ? $userrow['uid'] : 0;
    if (empty($uid)) {
        exit(json_encode(['code' => -2, 'msg' => '未登录']));
    }
    $success_count = 0;
    $fail_count = 0;
    $fail_orders = [];
    foreach ($posting_numbers as $posting_number) {
        $posting_number = trim($posting_number);
        if ($posting_number === '')
            continue;
        $row = $DB->getRow("SELECT * FROM ozon_order WHERE uid=:uid AND status='awaiting_deliver' AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if (!$row) {
            $fail_count++;
            $fail_orders[] = $posting_number;
            continue;
        }
        $sql = "UPDATE ozon_order SET packing_status = 1 WHERE uid = :uid AND posting_number = :posting_number";
        $params = [':uid' => $uid, ':posting_number' => $posting_number];
        $update_result = $DB->exec($sql, $params);
        if ($update_result !== false) {
            $success_count++;
        } else {
            $fail_count++;
            $fail_orders[] = $posting_number;
        }
    }
    $msg = "批量交运完成，成功: $success_count 条";
    if ($fail_count > 0) {
        $msg .= "，失败: $fail_count 条，订单号: " . implode(', ', $fail_orders);
    }
    exit(json_encode(['code' => 0, 'msg' => $msg, 'success' => $success_count, 'fail' => $fail_count, 'fail_orders' => $fail_orders]));
break;
case 'orders_updata': #单个同步订单，或者批量更新同步
    $posting_number = isset($_POST['posting_number']) ? daddslashes($_POST['posting_number']) : null;
    if ($posting_number) {
        $row = $DB->getRow("SELECT A.*,B.id,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        //exit(json_encode($row));
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $json = $client->postingfbsget($posting_number);
        
        $importer = new \lib\JsonImporter($DB,$Raconfig);
        //exit(json_encode($json));
        $data = $importer->importorder($json, false, $row);
        //exit(json_encode($data));
        if ($data) {
            if(isset($data['data']['errors']) and count($data['data']['errors'])>=1){
                exit(json_encode($data));
            }
            $code = 0;
        } else {
            $code = -1;
        }
        exit(json_encode(['code' => $code]));
    }else{
        if (function_exists("set_time_limit")) {
            @set_time_limit(0);
        }
        $id = intval($_POST['shop_ids']);
        $importer = new \lib\JsonImporter($DB,$Raconfig);
        $row = $DB->find('store', '*', ['id' => $id]);
        if($row){
            $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
            
            /*
            $filter = [
                'status' => 'cancelled',
                'since' => date('Y-m-d', strtotime('-3 days')),
                'to' => date('Y-m-d')
            ];
            $data = $client->getFbsPostingListV3($filter);
            if (isset($data['result']['postings'])) {
                foreach ($data['result']['postings'] as $items) {
                    $result = $importer->importorder($items, false, $row);
                    if(isset($result['data']['errors']) and $result['data']['errors']){
                        exit(json_encode($result));
                    }
                }
            }
            $data = $client->fbsunfulfilledlist(['cutoff_from' => date('Y-m-d H:i:s', strtotime('-6 days')), 'cutoff_to' => date('Y-m-d H:i:s', strtotime('+9 days'))]);
            if (isset($data['result']['postings'])) {
                foreach ($data['result']['postings'] as $item) {
                    $result = $importer->importorder($item, false, $row);
                    if(isset($result['data']['errors']) and $result['data']['errors']){
                        exit(json_encode($result));
                    }
                }
            }*/
            $offset = 0;
            while (true){
                $filter = [
                    'dir' => 'desc', // 从新到旧
                    'status' => 'delivered',
                    'since' => date('Y-m-d', strtotime('-90 days')),
                    'to' => date('Y-m-d'),
                    'offset'=>$offset
                ];
                $data = $client->getFbsPostingListV3($filter);
                if (isset($data['result']['postings'])) {
                    foreach ($data['result']['postings'] as $items) {
                        $result = $importer->importorder($items, false, $row);
                        if(isset($result['data']['errors']) and $result['data']['errors']){
                            exit(json_encode($result));
                        }
                        $offset++;
                    }
                }
                if(count($data['result']['postings'])===0){
                    break;
                }
            }
            
            $result = ['code'=>0];
        }else{
            $result = ['code'=>-1,'msg'=>'店铺数据不存在'];
        }
        exit(json_encode($result));
    }
break;
case 'update_order': #更新采购信息
    $posting_number = htmlspecialchars(strip_tags(trim($_POST['posting_number'])));
    $cost = htmlspecialchars(strip_tags(trim($_POST['cost'])));
    $purchase_orderSn = htmlspecialchars(strip_tags(trim($_POST['purchase_orderSn'])));
    $courierNumber = isset($_POST['courierNumber']) ? htmlspecialchars(strip_tags(trim($_POST['courierNumber']))) : '';
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if ($row) {
        if($cost)$updata['cost'] = $cost;
        if($purchase_orderSn){
            $updata['purchase_orderSn'] = $purchase_orderSn;
            if(strpos($purchase_orderSn, '-') === false){
                $updata['purchase_type'] = '1688';
            }else{
                $updata['purchase_type'] = 'pdd';
            }
        }
        if($courierNumber)$updata['courierNumber'] = $courierNumber;
        
        $updata['Purchase_date'] = $date;
        if ($DB->update('order', $updata, ['uid' => $uid, 'posting_number' => $posting_number])) {
            if ($userrow['package'] == 1) {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $data = $client->fbsshippackage($row);
                if ($data) {
                    $result['code'] = 0;
                } else {
                    $result['code'] = -3;
                    $result['msg'] = '订单备货失败，采购成功';
                }
            } else {
                $result['code'] = 0;
            }
        } else {
            $result['code'] = -1;
        }
    } else {
        $result = ['code' => -2, 'msg' => '不存在此订单'];
    }
    exit(json_encode($result));
break;
case 'cancel_purchase_relation': #强制取消关联订单
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }
    
    // 检查订单是否存在且属于当前用户
    $row = $DB->getRow("SELECT order_id FROM ozon_order WHERE uid = ? AND posting_number = ? LIMIT 1", [$uid, $posting_number]);
    if (!$row) {
        exit(json_encode(['code' => -2, 'msg' => '订单不存在或没有权限']));
    }
    
    // 清空采购相关字段
    $updateData = [
        'cost' => null,
        'purchase_orderSn' => null,
        'purchase_type' => null,
        'purchase_ok' => null,
        'courierNumber' => null,
        'purchase_lus' => null,
        'Purchase_date' => null
    ];
    
    if ($DB->update('order', $updateData, ['uid' => $uid, 'order_id' => $row['order_id']])) {
        $result = ['code' => 0, 'msg' => '采购关联已取消'];
    } else {
        $result = ['code' => -3, 'msg' => '取消关联失败：' . $DB->error()];
    }
    
    exit(json_encode($result));
break;
case 'order_package': #备货
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number))
        exit(json_encode(['code' => -2, 'msg' => 'posting_number No!']));
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if ($row) {
        addDelayedTask(['type'=>'product_order_updata','data'=>$row],rand(80,180));
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $data = $client->fbsshippackage($row);
        if ($data) {
            $result = ['code' => 0];
        } else {
            $result = ['code' => -3, 'msg' => json_encode($data)];
        }
    } else {
        $result = ['code' => -1, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
break;
case 'batch_order_package': #批量备货
    $posting_numbers = isset($_POST['posting_numbers']) ? $_POST['posting_numbers'] : [];
    if (!is_array($posting_numbers) || empty($posting_numbers)) {
        exit(json_encode(['code' => -1, 'msg' => '请选择要备货的订单']));
    }
    $uid = $islogin2 ? $userrow['uid'] : 0;
    if (empty($uid)) {
        exit(json_encode(['code' => -2, 'msg' => '未登录']));
    }
    $success_count = 0;
    $fail_count = 0;
    $fail_orders = [];
    $arrayrow = [];
    
    foreach ($posting_numbers as $posting_number) {
        
        $posting_number = daddslashes(trim($posting_number));
        if ($posting_number === '')
            continue;
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if ($row['status']=='awaiting_packaging') {
            
            if (!$row or !isset($row['ClientId'])) {
                $fail_count++;
                $fail_orders[] = $posting_number;
                continue;
            }
            $row['products'] = json_decode($row['products'],true);
            $products = [];
            foreach ($row['products'] as $item){
                $products[] = ['product_id'=>$item['sku'],'quantity'=>$item['quantity']];
            }
            $array = [
                'posting_number'=>$row['posting_number'],
                'products'=>$products
            ];
            if (!isset($arrayrow[$row['ClientId']])) {
                $arrayrow[$row['ClientId']] = [
                    'ClientId' => $row['ClientId'],
                    'key' => $row['key'],
                    'data' => []
                ];
            }
            
            $arrayrow[$row['ClientId']]['data'][] = $array;
            
            addDelayedTask(['type'=>'product_order_updata','data'=>$row],rand(80,180));
            
            // 更新manualstatus字段为1
            $DB->update('order', ['manualstatus' => 1], ['uid' => $uid, 'posting_number' => $posting_number]);
        }
    }
    // 发送消息到队列
    if (!empty($arrayrow)) {
        if(sendAMQPMessage('product_package', $arrayrow)){
            exit(json_encode(['code'=>0,'msg'=>'备货已经添加到列队任务，由于OZON延迟刷新订单状态，请等候5分钟后查询是否成功']));
        }
    }else{
        exit(json_encode(['code'=>-1,'msg'=>'提交的数据不能为空']));
    }
    exit(json_encode(['code'=>-2,'msg'=>'备货失败可能服务出现问题请联系相关销售']));
break;
case 'user_auto_switch': #切换备货模式
    $id = intval($_POST['id']);
    $status = intval($_POST['status']);
    if ($DB->update('user', ['package' => $status], ['uid' => $uid])) {
        $result = ['code' => 0];
    } else {
        $result = ['code' => -1];
    }
    exit(json_encode($result));
break;
case 'order_preview': #面单下载
    $posting_number = daddslashes($_POST['posting_number']);
    if ($posting_number) {
        $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
        if ($row) {
            $file_path = targetDateTime($row);
            if (file_exists($file_path[0])) {
                $result = ['code' => 0, 'msg' => '成功获取面单', 'url' => $file_path[1]];
            } else {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $url = $client->packagelabel($row);
                if ($url != false) {
                    $result = ['code' => 0, 'msg' => '成功获取面单', 'url' => $url];
                } else {
                    $result = ['code' => -3, 'msg' => '下载订单面单失败'];
                }
            }
        } else {
            $result = ['code' => -2, 'msg' => '不存在此订单'];
        }

    } else {
        $result = ['code' => -1, 'msg' => '未提交货物单号'];
    }
    exit(json_encode($result));
    break;
case 'getShops': #获取店铺
    $list = $DB->getAll("SELECT id, storename, ClientId, `key`, warehouses FROM ozon_store WHERE uid = ?", [$uid]);
    $list2 = [];
    foreach ($list as $row) {
        // 触发异步更新仓库（如果缺失）
        if (empty($row['warehouses']) || $row['warehouses'] === 'null') {
            async_update_warehouse($row['id'], $row['ClientId'], $row['key']);
            $row['warehouses'] = []; // 前端展示空数组
        } else {
            $row['warehouses'] = json_decode($row['warehouses'], true);
        }
        // 仅返回必要字段
        $setRedisarray[$row['id']] = ['ClientId'=>$row['ClientId'],'key'=>$row['key'],'storename'=>$row['storename']];
        $list2[] = [
            'id' => $row['id'],
            'storename' => $row['storename']
        ];
    }
    setRedis('store',json_encode($setRedisarray));
    exit(json_encode(['code' => 0, 'data' => $list2]));
break;
case '1688': #获取1688同款货源
    $posting_number = daddslashes($_POST['posting_number']);
    $data = $DB->find('order', '*', ['posting_number' => $posting_number, 'uid' => $uid]);
    $redis = new Redis();
    $redis->connect('127.0.0.1', 6379);
    $redis->setOption(Redis::OPT_READ_TIMEOUT, 5); // 设置读取超时
    $alref = $redis->get($data['sku'].'_imageId');
    if(empty($alref)){
        $url = "https://overseaplugin.1688.com/image/upload";
        $base64 = urlToBase64($data['primary_image'])??null;
        $post = json_encode(['imageBase64' => $base64, 'source' => 'www.100b.cn', 'terminalId' => 'Chrome_136.0.0.0', 'version' => '0.0.18']);
        $res = get_curl($url, $post, 0, 0, 0, 0, 0, ['Content-Type: application/json']);
        $json = json_decode($res, true);
        if ($json['success']) {
            $alref = $json['result'];
            $redis->set($data['sku'].'_imageId',$alref,1296000);
        }else{
            exit(json_encode(['code'=>-1,'msg'=>$json['retMsg'],'base64'=>$base64]));
        }
    }
    $redis->close();
    if (isset($json['success']) or $alref) {
        $url = "https://overseaplugin.1688.com/recommend/sameOfferRecommend";
        $post = json_encode(['beginPage' => 1, 'currency' => 'CNY', 'imageId' => $alref, 'keyword' => '', 'language' => 'zh_CN', 'pageSize' => 8, 'priceEnd' => '', 'priceStart' => '', 'region' => 'HK', 'source' => 'www.100b.cn', 'terminalId' => 'Chrome_136.0.0.0', 'version' => '0.0.18']);
        $res = get_curl($url, $post, 0, 0, 0, 0, 0, ['Content-Type: application/json']);
        $json = json_decode($res, true);
        if (empty($json['success'])) {
            exit(json_encode(['code'=>-1,'msg'=>$json['retMsg'],'base64'=>$base64,'imageId'=>$alref]));
        }
    }
    exit($res);
break;
case 'save_links':
    $json_data = file_get_contents('php://input');
    // 解析 JSON
    $data = json_decode(trim($json_data), true);
    $sku = intval($data['sku']);
    $posting_number = daddslashes($data['posting_number']);
    $links = $data['links'];
    if ($sku and $posting_number) {
        $data = $DB->find('management', '*', ['sku' => $sku]);
        if ($data) {
            if ($DB->update('management', ['purchase_url' => json_encode($links)], ['id' => $data['id'], 'uid' => $uid, 'sku' => $sku])) {
                $result = ['code' => 0];
            } else {
                $result = ['code' => -2, 'msg' => '更新数据失败' . $DB->error()];
            }
        } else {
            if ($DB->insert('management', ['uid' => $uid, 'sku' => $sku, 'purchase_url' => json_encode($links)])) {
                $result = ['code' => 0];
            } else {
                $result = ['code' => -3, 'msg' => '保存数据失败' . $DB->error()];
            }
        }
    } else {
        $result = ['code' => -1, 'msg' => '数据不齐全'];
    }
    exit(json_encode($result));
    break;
case 'get_urljh':
    $url = daddslashes($_POST['url']);
    $posting_number = daddslashes($_POST['posting_number']);
    $data = $DB->find('order', '*', ['posting_number' => $posting_number, 'uid' => $uid]);
    $host = $_SERVER['HTTP_HOST'];
    $domainParts = explode('.', $host);
    
    if (count($domainParts) === 1 || in_array(end($domainParts), ['localhost', 'test', 'local'])) {
        $mainDomain = $host;
    } else {
        $mainDomain = implode('.', array_slice($domainParts, -2, 2));
    }
    if (strpos($url, '1688.com') !== false) {
        $type = "1688";
    } else if (strpos($url, 'pinduoduo.com') !== false || strpos($url, 'yangkeduo.com') !== false) {
        $type = "pdd";
    }
    if($data['purchase_id']){
        $id = $data['purchase_id'];
        $result = ['code' => 0, 'url' => $url, 'posting_number' => $posting_number, 'id' => $id];
    }else if ($data) {
        $id = uniqid() . rand(11111, 99999);
        if ($DB->update('order', ['purchase_id' => $id], ['posting_number' => $posting_number])) {
            $result = ['code' => 0, 'url' => $url, 'posting_number' => $posting_number, 'id' => $id];
        } else {
            $result = ['code' => -1, 'msg' => '采购数据保存失败' . $DB->error()];
        }
    } else {
        $result = ['code' => -1, 'msg' => '数据不存在'];
    }
    if($id)setcookie("hterporderid", $id, time() + 300, "/", ".{$mainDomain}", isset($_SERVER['HTTPS']), true);
    if($type)setcookie("type", $type, time() + 300, "/", ".{$mainDomain}", isset($_SERVER['HTTPS']), true);
    exit(json_encode($result));
break;

case 'batch_print_order_tcpdf':
    try {
        // 解析输入的订单号列表
        $input = json_decode(file_get_contents('php://input'), true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("无效的JSON输入");
        }
        
        $posting_numbers = array_filter(array_map('trim', $input['posting_numbers'] ?? []));
        if (empty($posting_numbers)) {
            exit(json_encode(['code' => -1, 'msg' => '请选择要打印的订单']));
        }
        
        // 设置输出目录
        $output_dir = ROOT . 'user/temp_prints/';
        if (!is_dir($output_dir) && !mkdir($output_dir, 0755, true)) {
            throw new Exception("无法创建输出目录");
        }
        
        // 初始化TCPDF
        require_once ROOT . 'includes/vendor/tecnickcom/tcpdf/tcpdf.php';
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
        $pdf->SetPrintHeader(false);
        $pdf->SetPrintFooter(false);
        $pdf->SetAutoPageBreak(false, 0);
        
        // 检查Imagick扩展
        if (!extension_loaded('imagick')) {
            exit(json_encode(['code' => -500, 'msg' => '系统缺少Imagick扩展，请联系技术支持']));
        }
        
        // 处理每个订单面单
        $success_count = 0;
        $failed_orders = [];
        
        foreach ($posting_numbers as $posting_number) {
            // 获取订单信息
            $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
            
            if (!$row) {
                $failed_orders[] = $posting_number;
                continue;
            }
            
            // 获取面单文件路径
            $file_path = targetDateTime($row);
            $pdf_file = $file_path[0];
            
            // 检查面单是否存在，不存在则下载
            if (!file_exists($pdf_file)) {
                $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
                $url = $client->packagelabel($row);
                
                if ($url === false) {
                    $failed_orders[] = $posting_number;
                    continue;
                }
                
                // 等待面单生成完成
                sleep(1);
                if (!file_exists($pdf_file)) {
                    $failed_orders[] = $posting_number;
                    continue;
                }
            }
            
            // 使用Imagick处理PDF
            try {
                $imagick = new Imagick();
                $imagick->setResolution(300, 300); // 设置较高分辨率以保持质量
                $imagick->readImage($pdf_file);
                $imagick->setImageFormat('png');
                
                $num_pages = $imagick->getNumberImages();
                
                for ($i = 0; $i < $num_pages; $i++) {
                    $imagick->setIteratorIndex($i);
                    
                    // 保存临时图像
                    $temp_image = tempnam(sys_get_temp_dir(), 'pdfimg') . '.png';
                    file_put_contents($temp_image, $imagick);
                    
                    // 使用标准运单尺寸 75mm x 120mm
                    $width_mm = 75;
                    $height_mm = 120;
                    
                    // 设置边距以避免内容被遮挡
                    $margin = 2; // 2mm边距
                    $usable_width = $width_mm - ($margin * 2);
                    $usable_height = $height_mm - ($margin * 2);
                    
                    // 获取图像尺寸用于缩放计算
                    $size = getimagesize($temp_image);
                    $img_width = $size[0];
                    $img_height = $size[1];
                    
                    // 计算缩放比例以适应可用尺寸（减去边距）
                    $scale_x = $usable_width / (($img_width / 300) * 25.4);
                    $scale_y = $usable_height / (($img_height / 300) * 25.4);
                    $scale = min($scale_x, $scale_y) * 0.95; // 使用较小的缩放比例并额外缩小5%
                    
                    // 计算最终尺寸和居中位置
                    $final_width = (($img_width / 300) * 25.4) * $scale;
                    $final_height = (($img_height / 300) * 25.4) * $scale;
                    $x = ($width_mm - $final_width) / 2;
                    $y = ($height_mm - $final_height) / 2;
                    
                    $orientation = 'P'; // 固定为竖向
                    
                    // 添加到PDF
                    $pdf->AddPage($orientation, [$width_mm, $height_mm]);
                    $pdf->Image($temp_image, $x, $y, $final_width, $final_height, 'PNG', '', '', true, 300);
                    
                    // 删除临时文件
                    unlink($temp_image);
                }
                
                $success_count++;
            } catch (Exception $e) {
                $failed_orders[] = $posting_number;
                error_log("处理面单 {$posting_number} 出错: " . $e->getMessage());
            } finally {
                if (isset($imagick)) {
                    $imagick->clear();
                    $imagick->destroy();
                }
            }
        }
        
        // 如果没有成功处理任何面单
        if ($success_count === 0) {
            exit(json_encode([
                'code' => -2,
                'msg' => '没有找到可打印的订单面单',
                'failed_orders' => $failed_orders
            ]));
        }
        
        // 保存合并的PDF
        $output_file = 'merged_' . date('Ymd_His') . '.pdf';
        $output_path = $output_dir . $output_file;
        $pdf->Output($output_path, 'F');
          // 记录打印时间
        $operator = $uid; // 使用当前用户UID作为操作员
        foreach ($posting_numbers as $posting_number) {
            $order = $DB->getRow("SELECT order_id FROM ozon_order WHERE posting_number=?", [$posting_number]);
            if ($order) {
                $DB->exec("INSERT INTO order_times (order_id, posting_number, time_type, time_value, operator) VALUES (?, ?, 'print', NOW(), ?)", [
                    $order['order_id'],
                    $posting_number,
                    $operator
                ]);
            }
        }
        
        // 返回结果
        $result = [
            'code' => 0,
            'msg' => "成功合并 {$success_count} 个面单",
            'url' => '/user/temp_prints/' . $output_file,
            'success_count' => $success_count
        ];
        
        if (!empty($failed_orders)) {
            $result['failed_count'] = count($failed_orders);
            $result['failed_orders'] = $failed_orders;
        }
        
        exit(json_encode($result));
        
    } catch (Exception $e) {
        exit(json_encode([
            'code' => -500,
            'msg' => '系统错误: ' . $e->getMessage()
        ]));
    }
break;
case 'updata_kd': #更新物流
    if (function_exists("set_time_limit")) {
        @set_time_limit(0);
    }
    $posting_number = daddslashes($_POST['posting_number']);
    $row = $DB->getRow("SELECT * FROM ozon_order WHERE uid=:uid AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if(isset($row) and is_array($row)){
        if($row['purchase_type']==='pdd'){
            if (pddorder($row)) {
                $result = ['code' => 0];
            } else {
                $result = ['code' => 1, 'msg' => '暂不明原因无法获取数据'];
            }
        }else if($row['purchase_type']==='1688'){
            if(strpos($row['primary_image'], ',') === false){
                if (aliorder($row)) {
                    $result = ['code' => 0];
                } else {
                    $result = ['code' => 1, 'msg' => '暂不明原因无法获取数据'];
                }
            }else{
                $ex = explode(',',$row['purchase_orderSn']);
                foreach ($ex as $xe){
                    $row['purchase_orderSn'] = $xe;
                    if (aliorder($row)) {
                        $result = ['code' => 0];
                    } else {
                        $result = ['code' => 1, 'msg' => '暂不明原因无法获取数据'];
                    }
                }
            }
        }
    }else{
        $result = ['code'=>-1,'msg'=>'订单不存在'];
    }
    exit(json_encode($result));
break;
case 'get_order_notes': #获取订单备注
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    $row = $DB->getRow("SELECT OrderNotes FROM ozon_order WHERE uid=:uid AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if ($row) {
        $result = ['code' => 0, 'data' => ['OrderNotes' => $row['OrderNotes'] ?? '']];
    } else {
        $result = ['code' => -2, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
break;

case 'check_blacklist': #检查客户是否在黑名单中
    $customer_name = daddslashes($_POST['customer_name']);
    if (empty($customer_name)) {
        exit(json_encode(['code' => -1, 'msg' => '客户姓名不能为空']));
    }

    $exists = $DB->getColumn("SELECT COUNT(*) FROM blacklist WHERE uid=:uid AND customer_name=:customer_name", [':uid' => $uid, ':customer_name' => $customer_name]);
    $result = ['code' => 0, 'data' => ['exists' => $exists > 0]];
    exit(json_encode($result));
break;

case 'add_blacklist': #添加客户到黑名单
    $posting_number = daddslashes($_POST['posting_number']);
    $customer_name = daddslashes($_POST['customer_name']);
    $reason_type = intval($_POST['reason_type']);
    $reason_text = daddslashes($_POST['reason_text'] ?? '');
    
    if (empty($posting_number) || empty($customer_name) || !in_array($reason_type, [1, 2, 3, 4])) {
        exit(json_encode(['code' => -1, 'msg' => '参数错误']));
    }
    
    // 检查该订单是否已经添加过黑名单记录
    $exists = $DB->getColumn("SELECT COUNT(*) FROM blacklist WHERE uid=? AND posting_number=? AND customer_name=?", [$uid, $posting_number, $customer_name]);
    if ($exists > 0) {
        exit(json_encode(['code' => -2, 'msg' => '该订单的客户已在黑名单中']));
    }
    
    // 添加到黑名单
    $insert_result = $DB->exec("INSERT INTO blacklist (uid, posting_number, customer_name, reason_type, reason_text) VALUES (?, ?, ?, ?, ?)", 
        [$uid, $posting_number, $customer_name, $reason_type, $reason_text]);
    
    if ($insert_result) {
        $result = ['code' => 0, 'msg' => '已添加到黑名单'];
    } else {
        $result = ['code' => -3, 'msg' => '添加失败'];
    }
    exit(json_encode($result));
break;

case 'remove_blacklist': #从黑名单中移除客户
    $customer_name = daddslashes($_POST['customer_name']);
    $posting_number = daddslashes($_POST['posting_number'] ?? '');
    $remove_type = daddslashes($_POST['remove_type'] ?? 'all');
    
    if (empty($customer_name)) {
        exit(json_encode(['code' => -1, 'msg' => '客户姓名不能为空']));
    }

    if ($remove_type === 'single' && !empty($posting_number)) {
        // 只移除特定订单的黑名单记录
        $delete_result = $DB->exec("DELETE FROM blacklist WHERE uid=? AND customer_name=? AND posting_number=?", 
            [$uid, $customer_name, $posting_number]);
    } else {
        // 移除该客户的所有黑名单记录
        $delete_result = $DB->exec("DELETE FROM blacklist WHERE uid=? AND customer_name=?", 
            [$uid, $customer_name]);
    }
    
    if ($delete_result) {
        $result = ['code' => 0, 'msg' => '已从黑名单中移除'];
    } else {
        $result = ['code' => -2, 'msg' => '移除失败或客户不在黑名单中'];
    }
    exit(json_encode($result));
break;

case 'get_blacklist_count_by_customer': #获取客户的黑名单记录数量
    $customer_name = daddslashes($_POST['customer_name']);
    if (empty($customer_name)) {
        exit(json_encode(['code' => -1, 'msg' => '客户姓名不能为空']));
    }

    $count = $DB->getColumn("SELECT COUNT(*) FROM blacklist WHERE uid=? AND customer_name=?", [$uid, $customer_name]);
    $result = ['code' => 0, 'data' => ['count' => $count]];
    exit(json_encode($result));
break;

case 'get_customer_blacklist_history': #获取客户的黑名单历史记录
    $customer_name = daddslashes($_POST['customer_name']);
    if (empty($customer_name)) {
        exit(json_encode(['code' => -1, 'msg' => '客户姓名不能为空']));
    }

    $list = $DB->getAll("SELECT posting_number, reason_type, reason_text, created_at 
                        FROM blacklist 
                        WHERE uid=? AND customer_name=? 
                        ORDER BY created_at DESC", [$uid, $customer_name]);
    
    $reason_types = [
        1 => '频繁退货退款',
        2 => '恶意差评', 
        3 => '货到付款拒收',
        4 => '其他'
    ];
    
    foreach ($list as &$item) {
        $item['reason_type_text'] = $reason_types[$item['reason_type']] ?? '未知';
    }
    
    $result = ['code' => 0, 'data' => $list];
    exit(json_encode($result));
break;
case 'get_single_order': #获取单个订单数据
    $posting_number = daddslashes($_POST['posting_number']);
    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    // 使用与orders_all相同的查询逻辑
    $sql = "SELECT A.*, B.storename, B.ClientId, C.stocks, C.commissions, T.time_value as print_time 
            FROM ozon_order A 
            LEFT JOIN ozon_store B ON A.storeid=B.id 
            LEFT JOIN ozon_products C ON A.sku=C.sku 
            LEFT JOIN (
               SELECT posting_number, MAX(time_value) as time_value 
               FROM order_times 
               WHERE time_type = 'print' 
               GROUP BY posting_number
            ) T ON A.posting_number = T.posting_number 
            WHERE A.uid = ? AND A.posting_number = ?";
    
    $row = $DB->getRow($sql, [$uid, $posting_number]);
    
    if ($row) {
        $Specification = formatSpecification($row);
        $row['pj'] = $Specification[1];
        
        if(empty($row['weight'])){
            $row['weight'] = $Specification[0];
        }
        $row['money'] = $row['price'];
        $row['weight'] = round(($row['weight'] / 1000) * $row['quantity'], 2);
        $has_multiple_products = !empty($row['products']) && json_decode($row['products'], true) && count(json_decode($row['products'], true)) > 1;
        if ($has_multiple_products) {
            // 多商品订单：price字段已经是总价，quantity为1，直接使用
            // $row['price'] 保持不变，已经是总价
        } else {
            // 单商品订单：price字段是单价，需要乘以数量得到总价
            $row['price'] = round($row['price'] * $row['quantity'], 2);
        }
        $exp = explode(" ", $row['tpl_provider']);
        $row['wl'] = $exp[0];
        if ($exp[1] == 'Express') {
            $speed = "空运";
            $speedtx = "Express";
        } elseif ($exp[1] == 'Standard') {
            $speed = "陆空";
            $speedtx = "Standard";
        } else {
            $speed = "陆运";
            $speedtx = "Economy";
        }
        $row['provider'] = ozonmethodtype($row['tpl_provider']);
        $row['speed'] = $speed;
        
        if ($row['out_weight']) {
            $weight_type = 0;
            $row['out_weight'] = round($row['out_weight'] / 1000, 2);
            $weight = $row['out_weight'];
        } else {
            $weight_type = 1;
            $row['out_weight'] = "未出库";
            $weight = $row['weight'];
        }
        if(empty($row['delivery']))$row['delivery'] = calculateShippingCost(findShippingMethod(['provider' => $row['wl'],'method' => $row['provider'],'speed' => $speedtx]), $weight);
        if(empty($row['profit']))$row['profit'] = calculateResult($row['price'], $weight, $row['commission_percent'], $row['delivery'], $row['cost']);
        if($row['cost']>=0.01){
            $zmoney = round((float)$row['cost']-(float)$row['delivery']-(float)$row['commissions']*100,2);
            $row['costprofit'] = round(($row['profit']/$row['cost'])*100,2);
        }
        // 处理多商品信息
        $products = [];
        if (!empty($row['products'])) {
            $products = json_decode($row['products'], true);
        }
        
        if (!empty($products) && is_array($products) && count($products) > 1) {
            $row['has_multiple_products'] = true;
            $row['products_info'] = $products;
            
            // 为每个商品添加图片信息
            $images = [];
            if(strpos($row['primary_image'], ',')){
                $images = explode(',',$row['primary_image']);
                
                foreach ($row['products_info'] as $key => $product) {
                    $row['products_info'][$key]['image'] = $images[$key];
                }
            }else{
                $AMQPMessagetype = 'product_images_task';
                $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
                $channel = $connection->channel();
                $channel->queue_declare($AMQPMessagetype, false, true, false, false);
                $msg = new AMQPMessage(
                    json_encode($row),
                    [
                        'delivery_mode' => AMQPMessage::DELIVERY_MODE_PERSISTENT
                    ]
                );
                $channel->basic_publish($msg, '', $AMQPMessagetype);
                foreach ($row['products_info'] as $key => $product) {
                    $row['products_info'][$key]['image'] = '../assets/img/syncing.png';
                }
                if(isset($images)){
                    $DB->update('order', ['primary_image' => implode(',', $images)], ['posting_number' => $row['posting_number']]);
                }
            }
        } else {
            $row['has_multiple_products'] = false;
        }
        if ($row['customer']) {
            $customer = json_decode($row['customer'], true);
            $row['customer_name'] = $customer['name'];
            $row['region'] = $customer['address']['region'] . ($customer['address']['zip_code'] ? " / " . $customer['address']['zip_code'] : '');
            if ($customer['address']['country'] == 'Rossiya') {
                $row['country'] = "俄罗斯";
            }
        }
        
        // 处理佣金数据，确保显示的完整性 (get_single_order)
        $commission_percent = floatval($row['commission_percent'] ?? 0);
        $row['percent'] = $commission_percent;
        
        if($commission_percent > 0) {
            $row['commissions'] = round($row['money'] * ($commission_percent / 100), 2) . " ￥";
        } else {
            $row['commissions'] = "待获取";
        }
        
        $row['time'] = date("Y-m-d H:i:s",$row['time']);
        if($row['primary_image'])$row['primary_image'] = str_replace('https://cdn1.ozone.ru/', 'https://ir-2.ozonstatic.cn/', $row['primary_image']);
        $result = ['code' => 0, 'data' => $row];
    } else {
        $result = ['code' => -2, 'msg' => '订单不存在'];
    }
    exit(json_encode($result));
break;
case 'update_order_notes': #更新订单备注
    $posting_number = daddslashes($_POST['posting_number']);
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

    if (empty($posting_number)) {
        exit(json_encode(['code' => -1, 'msg' => '订单号不能为空']));
    }

    // 验证订单是否存在且属于当前用户
    $row = $DB->getRow("SELECT posting_number FROM ozon_order WHERE uid=:uid AND posting_number=:posting_number LIMIT 1", [':uid' => $uid, ':posting_number' => $posting_number]);
    if (!$row) {
        exit(json_encode(['code' => -2, 'msg' => '订单不存在']));
    }

    // 更新备注
    if ($DB->update('order', ['OrderNotes' => $notes], ['uid' => $uid, 'posting_number' => $posting_number])) {
        $result = ['code' => 0, 'msg' => '备注更新成功'];
    } else {
        $result = ['code' => -3, 'msg' => '备注更新失败'];
    }
    exit(json_encode($result));
break;
case 'update_price':
    $sku = daddslashes($_POST['sku']);
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key,B.currency_code FROM ozon_products A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.sku=:sku LIMIT 1", [':uid'=>$uid,':sku' => $sku]);
    
    if($row){
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        $row['price'] = $_POST['price'];
        $row['old_price'] = round(floatval($row['price']) * 1.2, 2);
        $row['offer_id'] = $_POST['offer_id'];
        $data = $client->upimportprices($row);
        if($data === true){
            $result = ['code'=>0,'msg'=>'价格修改成功'];
            // 同步最新的产品信息
            try {
                $importer = new \lib\JsonImporter($DB,$Raconfig);
                $items[] = $row['product_id']; 
                $data2 = $client->productinfolist($items,'product_id');
                $importer->importProducts($data2, false, $row);
            } catch (Exception $e) {
                error_log("Price update sync error: " . $e->getMessage());
                // 即使同步失败，价格更新成功也返回成功
            }
        }else{
            $result = ['code'=>-2,'msg'=>'价格修改失败，请检查产品ID或网络连接'];
        }
    }else{
        $result = ['code'=>-1,'msg'=>'获取数据失败'];
    }
    exit(json_encode($result));
break;



case 'update_product_weight': #更新商品重量
    $offer_id = daddslashes($_POST['offer_id']);
    $weight = daddslashes($_POST['weight']);
    
    if (empty($offer_id)) {
        exit(json_encode(['code' => -1, 'msg' => 'offer_id不能为空']));
    }
    
    if (empty($weight) || !is_numeric($weight)) {
        exit(json_encode(['code' => -1, 'msg' => '重量必须是有效数字']));
    }
    
    // 根据offer_id查找订单信息和店铺信息
    $row = $DB->getRow("SELECT A.*,B.ClientId,B.key FROM ozon_order A LEFT JOIN ozon_store B ON A.storeid=B.id WHERE A.uid=:uid AND A.offer_id=:offer_id LIMIT 1", [':uid' => $uid, ':offer_id' => $offer_id]);
    
    if (!$row) {
        exit(json_encode(['code' => -1, 'msg' => '未找到订单信息']));
    }
    
    try {
        // 创建API客户端
        $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
        
        // 构建重量更新请求数据，包含两个重量属性ID
        $weightData = [
            'items' => [
                [
                    'attributes' => [
                        [
                            'complex_id' => 0,
                            'id' => 4383, // OZON第一个重量属性ID
                            'values' => [
                                [
                                    'dictionary_value_id' => 0,
                                    'value' => (string)$weight
                                ]
                            ]
                        ],
                        [
                            'complex_id' => 0,
                            'id' => 4497, // OZON第二个重量属性ID
                            'values' => [
                                [
                                    'dictionary_value_id' => 0,
                                    'value' => (string)$weight
                                ]
                            ]
                        ]
                    ],
                    'offer_id' => (string)$offer_id
                ]
            ]
        ];
        
        // 调用API更新重量
        // 调试：记录发送的数据
        error_log("重量更新数据: " . json_encode($weightData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        $task_id = $client->attributesupdate($row, $weightData['items']);
        
        if ($task_id) {
            exit(json_encode(['code' => 0, 'msg' => '重量更新请求已提交', 'task_id' => $task_id]));
        } else {
            exit(json_encode(['code' => -1, 'msg' => '重量更新失败']));
        }
        
    } catch (Exception $e) {
        exit(json_encode(['code' => -1, 'msg' => '重量更新异常: ' . $e->getMessage()]));
    }
break;


case 'get_ordersdata':  ##  刷新物流信息
    $type = isset($_POST['type'])?daddslashes(trim($_POST['type'])):'pdd';
    $rs = $DB->getAll("SELECT purchase_orderSn,posting_number FROM ozon_order WHERE uid='{$uid}' AND status='awaiting_deliver' AND packing_status=0  AND purchase_ok!=3 AND purchase_type='{$type}' AND purchase_orderSn IS NOT NULL");
    if($rs){
        foreach ($rs as $row){
            if($type==='pdd'){
                if (strpos($row['purchase_orderSn'], ',') !== false){
                    $ex = explode(',',$row['purchase_orderSn']);{
                        foreach ($ex as $value){
                            $orders[] = $value;
                        }
                    }
                }else{
                    $orders[] = $row['purchase_orderSn'];
                }
            }else{
                $orders[] = $row['posting_number'];
            }
        }
        $uniqueOrders = array_unique($orders); // 移除重复订单
        $orders = array_values($uniqueOrders); // 重置数组索引
        $result = ['code'=>0,'orders'=>$orders,'count'=>count($orders)];
    }else{
        $result = ['code'=>-1,'msg'=>'没有订单数据'];
    }
    exit(json_encode($result));
break;

}


// 添加优化函数，用于处理待处理子标签的查询
function optimize_pending_query($sql, $status) {
    global $DB;
    // 针对不同的子标签状态优化SQL查询
    if ($status == 'pending') {
        // 对于"全部"标签，使用索引优化查询
        //$sql = str_replace("SELECT", "SELECT ", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    } else if ($status == 'not_purchased') {
        // 对于"未采购"标签，添加索引提示
        //$sql = str_replace("SELECT", "SELECT ", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    } else if ($status == 'purchased') {
        // 对于"已采购"标签，添加索引提示
        //$sql = str_replace("SELECT", "SELECT ", $sql);
        $sql .= " ORDER BY in_process_at DESC";
    }
    
    return $sql;
}