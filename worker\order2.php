<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';
use PhpAmqpLib\Connection\AMQPStreamConnection;
use PhpAmqpLib\Message\AMQPMessage;

// 连接到 RabbitMQ
try {
    $connection = new AMQPStreamConnection($Raconfig['host'], $Raconfig['port'], $Raconfig['user'], $Raconfig['pwd']);
    $channel = $connection->channel();
    $channel->queue_declare('order', false, true, false, false); // 持久化队列
} catch (Exception $e) {
    die("连接失败: " . $e->getMessage());
}

echo " [*] 等待消息. 按 CTRL+C 退出\n";

// 定义回调函数
$callback = function ($msg) {
    $data = json_decode($msg->body, true);
    $logger = new ConsumerLogger();
    
    $startTime = microtime(true);
    $messageId = uniqid();
    //echo "\n [x] 收到 ", $data['type']," ID: ", $data['id']??$data['data']['ClientId'], "  时间: ".date("Y-m-d H:i:s"),"\n";
    //$logger->info("[$messageId] 收到 ". $data['type']." ID: ".$data['data']['ClientId']);
    $dbconfig = json_decode(dbconfig,true);
    $DB = new \lib\PdoHelper($dbconfig);
    
    switch ($data['type']) {
        case 'orderadd':
            $time = time();
            orderadd($data['data'],$messageId);   #获取新的订单，或刷新订单
            $s = time()-$time;
            $logger->info("[$messageId] 店铺：".$data['data']['storename'].'-ID：'.$data['data']['id'].'，'.$s.'/s 处理完毕。');
        break;
        case 'productsync':
            if (!productsync($data['id'])) {  #商品同步
                // 如果获取锁失败，拒绝消息并重新入队
                $msg->delivery_info['channel']->basic_nack($msg->delivery_info['delivery_tag'], false, true);
                return;
            }
        break;
        default:
            processSlowTask($data);
        break;
    }
    $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']); // 正确确认方式
};

// 公平分发（同一时间只处理一个消息）
$channel->basic_qos(null, 1, null);

// 订阅队列（修正参数顺序）
$channel->basic_consume('order', '', false, false, false, false, $callback);

// 保持监听
while (count($channel->callbacks)) {
    $channel->wait();
}

// 关闭连接
$channel->close();
$connection->close();

function processSlowTask($data) {
    file_put_contents('log.txt', print_r($data, true) . "\n", FILE_APPEND);
}


function orderadd($row,$messageId){
    global $DB,$Raconfig;
    $logger = new ConsumerLogger();
    $redis = new Redis();$redis->connect('127.0.0.1', 6379);
    if($redis->get('store_'.$row['ClientId'].'_UID'.$row['uid'])){
        $redis->close();
        return true;
    }
    
    $importer = new \lib\JsonImporter($DB,$Raconfig);
    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    $client->productinfolimit($row);
    if($row['actions']==1){
        $client->actionsproducts();
    }
    // 1. 获取已取消订单 (近15天)
    $filter = [
        'status' => 'cancelled',
        'since' => date('Y-m-d', strtotime('-15 days')),
        'to' => date('Y-m-d')
    ];
    $data = $client->getFbsPostingListV3($filter);
    if($data['code']){
        if($data['message']=='Company is blocked, please contact support'){
            $store['apistatus'] = 3; # 公司已封锁，请联系支持
        }else if($data['message']=='Api-key is deactivated, use another one or generate a new one'){
            $store['apistatus'] = 2; # Api-key 已失效
        }
        $DB->update('store', $store, ['ClientId'=>$row['ClientId']]);
        $redis->set('store_'.$row['ClientId'].'_UID'.$row['uid'],$data['message'],3600);
        //echo(json_encode($data).'</br>'.$row['ClientId'].'</br></br>');
    }
    if(isset($data['result']['postings']) && $data['result']['postings']){
        $logger->info("[$messageId] getFbsPostingListV3 cancelled ".count($data['result']['postings']));
        foreach ($data['result']['postings'] as $item) {
            if(empty($item['posting_number']))continue;
            $logger->info("[$messageId] cancelled {$item['posting_number']} ");
            $result = $importer->importorder($item, false, $row);
            if($result['errors']){
                $logger->error("[$messageId] 已取消订单导入失败: " . json_encode($result['errors']));
            }
        }
    }
    
    // 2. 获取等待打包订单 (近3天)
    $filter = [
        'status' => 'awaiting_packaging',
        'since' => date('Y-m-d', strtotime('-3 days')),
        'to' => date('Y-m-d', strtotime('+1 days'))
    ];
    $data = $client->getFbsPostingListV3($filter);
    if(!$data['code'] && isset($data['result']['postings']) && $data['result']['postings']){
        $logger->info("[$messageId] getFbsPostingListV3 awaiting_packaging ".count($data['result']['postings']));
        foreach ($data['result']['postings'] as $item) {
            if(empty($item['posting_number']))continue;
            $logger->info("[$messageId] awaiting_packaging {$item['posting_number']} ");
            $result = $importer->importorder($item, false, $row);
            if($result['errors']){
                $logger->error("[$messageId] 等待打包订单导入失败: " . json_encode($result['errors']));
            }
        }
    }
    
    // 3. 获取所有未完成订单 (未来16天)
    $data = $client->fbsunfulfilledlist(['cutoff_from'=>date('Y-m-d H:i:s', strtotime('-1 days')),'cutoff_to'=>date('Y-m-d H:i:s', strtotime('+16 days'))]);
    if(isset($data['result']['postings'])){
        $logger->info("[$messageId] fbsunfulfilledlist ".count($data['result']['postings']));
        foreach ($data['result']['postings'] as $item){
            if(empty($item['posting_number']))continue;
            $logger->info("[$messageId] unfulfilled {$item['posting_number']} status:{$item['status']} ");
            
            // 特殊处理：等待发货状态的订单需要打印标签
            if($item['status']=='awaiting_deliver'){
                $orderData = $DB->find('order', '*', ['order_id' => $item['order_id']]);
                if($orderData && $orderData['packagelabel']==0){
                    if($client->packagelabel($orderData)){
                        $DB->update('order', ['packagelabel'=>1], ['order_id'=>$item['order_id']]);
                        $logger->info("[$messageId] 订单 {$item['posting_number']} 标签打印成功");
                    }
                }
            }
            
            $result = $importer->importorder($item, false, $row);
            if($result['errors']){
                $logger->error("[$messageId] 未完成订单导入失败: " . json_encode($result['errors']));
            }
        }
    }
    
    $redis->close();
}

// 日志类
class ConsumerLogger {
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }
    
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }
    
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }
    
    public function debug($message, $context = []) {
        $this->log('DEBUG', $message, $context);
    }
    
    private function log($level, $message, $context = []) {
        $logEntry = sprintf(
            "[%s] [%s] %s %s\n",
            date('Y-m-d H:i:s'),
            $level,
            $message,
            !empty($context) ? json_encode($context, JSON_UNESCAPED_UNICODE) : ''
        );
        
        file_put_contents(__DIR__.'/logs/consumer.log', $logEntry, FILE_APPEND);
        echo $logEntry;
    }
}