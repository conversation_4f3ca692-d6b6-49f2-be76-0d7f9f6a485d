<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

// 从命令行参数获取配置，或使用默认值
$storeId = $argv[1] ?? 37;
$status = $argv[2] ?? 'delivered';  // awaiting_packaging, awaiting_deliver, delivering, delivered, all
$days = $argv[3] ?? 90;  // 查询天数
$debug = isset($argv[4]) && $argv[4] === 'debug'; // 是否开启调试模式

// 处理特殊情况：当第一个参数是 'all' 时，表示查询所有店铺
$queryAllStores = false;
if ($storeId === 'all') {
    $queryAllStores = true;
    // 重新调整参数位置
    $status = $argv[2] ?? 'delivered';
    $days = $argv[3] ?? 90;
    $debug = isset($argv[4]) && $argv[4] === 'debug';
}

echo "=== Ozon 订单同步脚本 ===\n";
if ($queryAllStores) {
    echo "店铺模式: 查询所有有效店铺\n";
} else {
    echo "店铺ID: {$storeId}\n";
}
echo "订单状态: {$status}\n";
echo "查询天数: {$days}\n";
echo "调试模式: " . ($debug ? '开启' : '关闭') . "\n\n";

// 获取店铺数据
if ($queryAllStores) {
    // 首先查询所有店铺，查看实际的状态值
    $allStores = $DB->findAll('store', ['id', 'ClientId', 'status'], []);
    echo "数据库中所有店铺信息:\n";
    foreach ($allStores as $store) {
        echo "- ID: {$store['id']}, ClientId: {$store['ClientId']}, 状态: " . ($store['status'] ?? 'NULL') . "\n";
    }
    
    // 尝试不同的状态条件查询有效店铺
    $stores = $DB->findAll('store', '*', ['status' => 'active']);
    if (empty($stores)) {
        // 如果没有status为active的店铺，尝试查询所有有ClientId和key的店铺
        echo "\n没有找到status='active'的店铺，尝试查询有完整配置的店铺...\n";
        $stores = $DB->findAll('store', '*', []);
        $validStores = [];
        foreach ($stores as $store) {
            if (!empty($store['ClientId']) && !empty($store['key'])) {
                $validStores[] = $store;
            }
        }
        $stores = $validStores;
    }
    
    if (empty($stores)) {
        echo "错误: 没有找到有ClientId和key配置的店铺\n";
        exit(1);
    }
    echo "查询所有店铺模式，找到 " . count($stores) . " 个可用店铺\n\n";
} else {
    $row = $DB->find('store', '*', ['id' => $storeId]);
    if (!$row) {
        echo "错误: 找不到ID为{$storeId}的店铺\n";
        
        // 显示可用店铺
        echo "\n可用店铺列表:\n";
        $stores = $DB->findAll('store', ['id', 'ClientId', 'status'], []);
        foreach ($stores as $store) {
            echo "- ID: {$store['id']}, ClientId: {$store['ClientId']}, 状态: " . ($store['status'] ?? '未知') . "\n";
        }
        exit(1);
    }
    $stores = [$row]; // 将单个店铺包装成数组，统一处理
}
// 处理店铺数据（支持单个店铺或所有店铺）
$allResults = [];
$totalProcessedStores = 0;

foreach ($stores as $storeIndex => $row) {
    $currentStoreId = $row['id'];
    echo "\n=== 处理店铺 {$currentStoreId} (" . ($storeIndex + 1) . "/" . count($stores) . ") ===\n";
    echo "ClientId: {$row['ClientId']}\n";
    
    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    
    $offset = 0;
    $orderNumbers = [];
    $totalOrders = 0;
    
    // 计算时间范围（根据时间计算规范使用准确格式，结束时间设为昨天）
    $since = new DateTime("-{$days} days", new DateTimeZone('UTC'));
    $to = new DateTime('yesterday', new DateTimeZone('UTC'));
    $to->setTime(23, 59, 59); // 设置为昨天的23:59:59
    
    echo "时间范围: " . $since->format('Y-m-d H:i:s') . " 到 " . $to->format('Y-m-d H:i:s') . "\n\n";
    
    // 根据订单同步系统规范，使用您提供的正确请求格式
    // 完整的请求参数结构
    $requestParams = [
        "dir" => "ASC",
        "filter" => [
            "since" => "2025-06-03T11:47:39.878Z",
            "status" => "delivered",
            "to" => "2025-09-02T11:47:39.878Z"
        ],
        "limit" => 100,
        "offset" => 0
    ];
    
    echo "正在同步店铺 {$currentStoreId} 的订单数据...\n";
    
    while (true) {
        // 更新offset参数
        $requestParams['offset'] = $offset;
        
        // 简化输出 - 只显示进度
        if ($offset === 0) {
            echo "正在获取订单数据";
        }
        echo "\r进度: 第" . (intval($offset/100) + 1) . "页 | 已获取: {$totalOrders}个";
        flush();
        
        try {
            // 直接使用完整的请求参数，绕过sanitizeFilter处理
            $url = 'https://api-seller.ozon.ru/v3/posting/fbs/list';
            
            // 创建标准的cURL请求
            $ch = curl_init();
            $headers = [
                'Client-Id: ' . $row['ClientId'],
                'Api-Key: ' . $row['key'],
                'Content-Type: application/json'
            ];
            
            // 使用标准JSON编码，不使用PRETTY_PRINT
            $jsonData = json_encode($requestParams, JSON_UNESCAPED_UNICODE);
            
            // 移除调试输出，减少干扰
            if ($debug) {
                echo "\n发送的JSON数据: " . $jsonData . "\n";
            }
            
            curl_setopt_array($ch, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POSTFIELDS => $jsonData,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10
            ]);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            // 只在错误时显示 HTTP 状态
            if ($httpCode !== 200 || $curlError) {
                echo "\nHTTP状态码: " . $httpCode . "\n";
                if ($curlError) {
                    echo "cURL错误: " . $curlError . "\n";
                }
            }
            
            $data = json_decode($response, true);
            
            // 只在错误或调试模式时显示API响应
            if (isset($data['error']) || ($debug && $offset === 0)) {
                echo "\nAPI原始响应: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
            }
            
            // 检查是否有错误信息
            if (isset($data['error'])) {
                echo "API返回错误: " . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . "\n";
                break;
            }
            
            if (isset($data['result']['postings']) && !empty($data['result']['postings'])) {
                $currentBatchCount = count($data['result']['postings']);
                // 只在调试模式显示详细信息
                if ($debug) {
                    echo "\n获取到 {$currentBatchCount} 个订单\n";
                }
                
                foreach ($data['result']['postings'] as $item) {
                    $orderNumbers[] = [
                        'posting_number' => $item['posting_number'],
                        'status' => $item['status'] ?? '未知',
                        'created_at' => $item['created_at'] ?? '',
                        'in_process_at' => $item['in_process_at'] ?? ''
                    ];
                    $totalOrders++;
                }
                
                $offset += $currentBatchCount;
                
                // 如果返回的数据量小于limit，说明没有更多数据了
                if ($currentBatchCount < 100) {
                    echo "\n\u2713 数据获取完成!\n";
                    break;
                }
                
                // 添加延迟避免API限流
                usleep(500000); // 0.5秒延迟
            } else {
                echo "\n✗ 没有更多订单数据\n";
                break;
            }
        } catch (Exception $e) {
            echo "API调用失败: " . $e->getMessage() . "\n";
            break;
        }
        
        // 防止无限循环，设置最大偏移量
        if ($offset >= 10000) {
            echo "已达到最大偏移量限制\n";
            break;
        }
    }
    
    // 保存订单号到JSON文件，文件名包含状态和天数信息
    $filename = "{$currentStoreId}_{$status}_{$days}days.json";
    $filePath = __DIR__ . '/data/' . $filename;
    
    // 确保目录存在
    if (!is_dir(dirname($filePath))) {
        mkdir(dirname($filePath), 0755, true);
    }
    
    // 写入JSON文件
    file_put_contents($filePath, json_encode($orderNumbers, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // **修复: 同步更新到 order 表（而不是 ozon_order 表）**
    echo "\n开始同步更新订单状态到 order 表...\n";
    $updateStats = updateOrderStatuses($orderNumbers, $currentStoreId, $debug);
    
    echo "\n" . str_repeat('=', 50) . "\n";
    echo "店铺 {$currentStoreId} 订单同步结果:\n";
    echo str_repeat('=', 50) . "\n";
    echo "✓ 获取订单数量: {$totalOrders}\n";
    echo "✓ 数据已保存到: {$filePath}\n";
    
    // 显示数据库更新统计
    echo "\n数据库更新统计:\n";
    echo "- 处理订单: {$updateStats['processed']}\n";
    echo "- 成功更新: {$updateStats['updated']} ✅\n";
    echo "- 未找到: {$updateStats['not_found']}\n";
    echo "- 状态未变: {$updateStats['no_change']}\n";
    echo "- 错误: {$updateStats['errors']} " . ($updateStats['errors'] > 0 ? "❌" : "✅") . "\n";
    
    // 显示状态统计
    if (!empty($orderNumbers)) {
        $statusCount = [];
        foreach ($orderNumbers as $order) {
            $statusCount[$order['status']] = ($statusCount[$order['status']] ?? 0) + 1;
        }
        
        echo "\n订单状态分布:\n";
        foreach ($statusCount as $orderStatus => $count) {
            $percentage = round(($count / $totalOrders) * 100, 1);
            echo "- {$orderStatus}: {$count} 个 ({$percentage}%)\n";
        }
    }
    echo str_repeat('=', 50) . "\n";
    
    // 收集每个店铺的结果
    $storeResult = [
        'store_id' => $currentStoreId,
        'client_id' => $row['ClientId'],
        'filename' => $filename,
        'order_count' => $totalOrders,
        'file_path' => $filePath,
        'status_count' => $statusCount ?? [],
        'update_stats' => $updateStats  // 新增数据库更新统计
    ];
    
    $allResults[] = $storeResult;
    $totalProcessedStores++;
    
    // 如果是多店铺模式，添加间隔
    if (count($stores) > 1 && $storeIndex < count($stores) - 1) {
        echo "\n等待1秒后处理下一个店铺...\n";
        sleep(1);
    }
}

// 生成最终结果
if ($queryAllStores) {
    $result = [
        'code' => 0,
        'message' => "所有店铺订单数据同步完成",
        'processed_stores' => $totalProcessedStores,
        'total_stores' => count($stores),
        'stores_data' => $allResults,
        'status_filter' => $status,
        'days' => $days,
        'database_update_summary' => [
            'total_processed' => array_sum(array_column($allResults, 'update_stats.processed')),
            'total_updated' => array_sum(array_column($allResults, 'update_stats.updated')),
            'total_errors' => array_sum(array_column($allResults, 'update_stats.errors'))
        ],
        'time_range' => [
            'since' => $since->format('Y-m-d H:i:s'),
            'to' => $to->format('Y-m-d H:i:s')
        ]
    ];
} else {
    $storeData = $allResults[0] ?? null;
    if ($storeData) {
        $result = [
            'code' => 0,
            'message' => '订单数据已保存',
            'filename' => $storeData['filename'],
            'order_count' => $storeData['order_count'],
            'file_path' => $storeData['file_path'],
            'status_filter' => $status,
            'days' => $days,
            'update_stats' => $storeData['update_stats'] ?? [],
            'time_range' => [
                'since' => $since->format('Y-m-d H:i:s'),
                'to' => $to->format('Y-m-d H:i:s')
            ]
        ];
    } else {
        $result = [
            'code' => -1,
            'msg' => '店铺数据处理失败'
        ];
    }
}

echo "\n" . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";

/**
 * 批量更新订单状态到 order 表
 * @param array $orderNumbers 订单数据数组
 * @param int $storeId 店铺ID
 * @param bool $debug 是否开启调试模式
 * @return array 更新统计结果
 */
function updateOrderStatuses($orderNumbers, $storeId, $debug = false) {
    global $DB;
    
    $stats = [
        'processed' => 0,
        'updated' => 0,
        'not_found' => 0,
        'no_change' => 0,
        'errors' => 0
    ];
    
    if (empty($orderNumbers)) {
        return $stats;
    }
    
    // 按状态分组，准备批量更新
    $statusGroups = [];
    $postingNumbers = [];
    
    foreach ($orderNumbers as $order) {
        if (!isset($order['posting_number']) || !isset($order['status'])) {
            $stats['errors']++;
            continue;
        }
        
        $postingNumber = $order['posting_number'];
        $status = $order['status'];
        
        // 按状态分组
        if (!isset($statusGroups[$status])) {
            $statusGroups[$status] = [];
        }
        $statusGroups[$status][] = $postingNumber;
        $postingNumbers[] = $postingNumber;
        $stats['processed']++;
    }
    
    if (empty($postingNumbers)) {
        return $stats;
    }
    
    if (!$debug) {
        echo "  正在批量更新 " . count($postingNumbers) . " 个订单...";
        flush();
    } else {
        echo "  准备批量更新 " . count($postingNumbers) . " 个订单...\n";
    }
    
    try {
        // 根据外部函数调用规范，检查数据库函数是否存在
        if (!method_exists($DB, 'beginTransaction') || !method_exists($DB, 'commit') || !method_exists($DB, 'rollback')) {
            echo "  警告: 数据库不支持事务，使用普通批量更新\n";
            return updateOrderStatusesSimple($orderNumbers, $storeId, $debug);
        }
        
        // 首先批量查询现有订单状态 - 使用 order 表而不是 ozon_order 表
        $placeholders = str_repeat('?,', count($postingNumbers) - 1) . '?';
        $sql = "SELECT posting_number, status FROM order WHERE posting_number IN ({$placeholders})";
        
        if ($debug) {
            echo "  执行查询SQL: {$sql}\n";
            echo "  查询参数数量: " . count($postingNumbers) . "\n";
        }
        
        $existingOrders = $DB->getAll($sql, $postingNumbers);
        
        // 检查查询结果
        if ($existingOrders === false) {
            throw new Exception("数据库查询失败: " . implode(', ', $DB->errorInfo()));
        }
        
        // 确保返回的是数组
        if (!is_array($existingOrders)) {
            $existingOrders = [];
        }
        
        // 建立现有订单的状态映射
        $existingStatusMap = [];
        foreach ($existingOrders as $order) {
            $existingStatusMap[$order['posting_number']] = $order['status'];
        }
        
        $stats['not_found'] = $stats['processed'] - count($existingOrders);
        
        if ($debug && $stats['not_found'] > 0) {
            echo "  警告: {$stats['not_found']} 个订单在数据库中不存在\n";
        }
        
        $statusChanges = [];
        $updateTime = date('Y-m-d H:i:s');
        
        // 按状态批量更新
        if (!$debug) {
            // 简化模式：只显示结果
            echo " 完成!\n";
        } else {
            echo "  第三阶段: 批量更新订单状态...\n";
        }
        
        foreach ($statusGroups as $newStatus => $orderList) {
            if ($debug) {
                echo "    处理状态 '{$newStatus}': " . count($orderList) . " 个订单\n";
            }
            // 过滤出需要更新的订单（排除不存在的和状态相同的）
            $needUpdateOrders = [];
            foreach ($orderList as $postingNumber) {
                if (!isset($existingStatusMap[$postingNumber])) {
                    continue; // 订单不存在，跳过
                }
                
                $oldStatus = $existingStatusMap[$postingNumber];
                if ($oldStatus === $newStatus) {
                    $stats['no_change']++;
                    if ($debug) {
                        echo "  - {$postingNumber}: 状态未变 ({$newStatus})\n";
                    }
                    continue;
                }
                
                $needUpdateOrders[] = $postingNumber;
                
                // 记录状态变更统计
                $changeKey = $oldStatus . ' → ' . $newStatus;
                $statusChanges[$changeKey] = ($statusChanges[$changeKey] ?? 0) + 1;
            }
            
            // 批量更新相同状态的订单
            if (!empty($needUpdateOrders)) {
                $chunkSize = 100; // 每批次处理100个订单，提高稳定性
                $chunks = array_chunk($needUpdateOrders, $chunkSize);
                
                foreach ($chunks as $chunkIndex => $chunk) {
                    $chunkCount = count($chunk);
                    
                    if ($debug) {
                        echo "      批次 " . ($chunkIndex + 1) . ": {$chunkCount} 个订单...";
                    }
                    
                    try {
                        // 根据 batch_update_status.php 的成功实践，每个小批次使用独立事务
                        $DB->beginTransaction();
                        
                        $placeholders = str_repeat('?,', count($chunk) - 1) . '?';
                        $sql = "UPDATE order SET status = ?, updated_at = ? WHERE posting_number IN ({$placeholders})";
                        $params = array_merge([$newStatus, $updateTime], $chunk);
                        
                        if ($debug) {
                            echo "\n        执行SQL: {$sql}\n";
                            echo "        参数: 状态={$newStatus}, 订单数量=" . count($chunk) . "\n";
                        }
                        
                        $result = $DB->exec($sql, $params);
                        
                        if ($debug) {
                            echo "        SQL执行结果: " . ($result !== false ? '成功' : '失败') . "\n";
                            if ($result !== false) {
                                echo "        影响行数: {$result}\n";
                            }
                        }
                        
                        if ($result !== false) {
                            $DB->commit();
                            $updatedCount = count($chunk);
                            $stats['updated'] += $updatedCount;
                            
                            if ($debug) {
                                echo " ✓ 成功\n";
                                if (count($chunk) <= 5) {
                                    foreach ($chunk as $pn) {
                                        echo "        ✓ {$pn} → {$newStatus}\n";
                                    }
                                }
                            }
                        } else {
                            $DB->rollback();
                            $stats['errors'] += count($chunk);
                            
                            // 获取数据库错误信息
                            $errorInfo = $DB->errorInfo();
                            $errorMsg = isset($errorInfo[2]) ? $errorInfo[2] : '未知错误';
                            
                            if ($debug || !empty($errorMsg)) {
                                echo " ✗ 失败: {$errorMsg}\n";
                            } elseif (!$debug) {
                                echo " ✗";
                            }
                        }
                        
                    } catch (Exception $e) {
                        // 确保在异常情况下回滚事务
                        try {
                            $DB->rollback();
                        } catch (Exception $rollbackException) {
                            // 忽略回滚错误
                        }
                        $stats['errors'] += count($chunk);
                        $errorMsg = $e->getMessage();
                        echo " ✗ 异常: {$errorMsg}\n";
                        
                        // 记录详细错误信息供调试
                        if ($debug) {
                            echo "        异常堆栈: " . $e->getTraceAsString() . "\n";
                        }
                    }
                    
                    // 短暂延迟，避免数据库压力过大
                    if ($chunkIndex < count($chunks) - 1) {
                        usleep(10000); // 10ms延迟
                    }
                }
            }
        }
        
        // 显示状态变更统计
        if (!empty($statusChanges)) {
            echo "\n  批量更新完成，状态变更统计:\n";
            foreach ($statusChanges as $change => $count) {
                echo "    {$change}: {$count} 个\n";
            }
        }
        
    } catch (Exception $e) {
        // 处理查询阶段的错误
        $stats['errors'] = $stats['processed'];
        echo "  ✗ 数据库查询失败: " . $e->getMessage() . "\n";
    }
    
    return $stats;
}

/**
 * 简化版批量更新（不使用事务）
 */
function updateOrderStatusesSimple($orderNumbers, $storeId, $debug = false) {
    global $DB;
    
    $stats = [
        'processed' => 0,
        'updated' => 0,
        'not_found' => 0,
        'no_change' => 0,
        'errors' => 0
    ];
    
    // 按状态分组进行批量更新
    $statusGroups = [];
    foreach ($orderNumbers as $order) {
        if (!isset($order['posting_number']) || !isset($order['status'])) {
            $stats['errors']++;
            continue;
        }
        
        $status = $order['status'];
        if (!isset($statusGroups[$status])) {
            $statusGroups[$status] = [];
        }
        $statusGroups[$status][] = $order['posting_number'];
        $stats['processed']++;
    }
    
    $updateTime = date('Y-m-d H:i:s');
    
    // 按状态批量更新
    foreach ($statusGroups as $newStatus => $postingNumbers) {
        $chunkSize = 100; // 更小的批次
        $chunks = array_chunk($postingNumbers, $chunkSize);
        
        foreach ($chunks as $chunk) {
            try {
                $placeholders = str_repeat('?,', count($chunk) - 1) . '?';
                $sql = "UPDATE order SET status = ?, updated_at = ? WHERE posting_number IN ({$placeholders})";
                $params = array_merge([$newStatus, $updateTime], $chunk);
                
                $result = $DB->exec($sql, $params);
                
                if ($result !== false) {
                    $stats['updated'] += count($chunk);
                    if ($debug) {
                        echo "  ✓ 简化批量更新 " . count($chunk) . " 个订单为 {$newStatus}\n";
                    }
                } else {
                    $stats['errors'] += count($chunk);
                }
            } catch (Exception $e) {
                $stats['errors'] += count($chunk);
                if ($debug) {
                    echo "  ✗ 简化批量更新失败: " . $e->getMessage() . "\n";
                }
            }
        }
    }
    
    return $stats;
}

// 使用说明
if (empty($argv[1])) {
    echo "\n=== 使用说明 ===\n";
    echo "php plod.php [店铺ID] [状态] [天数] [debug]\n";
    echo "\n参数说明:\n";
    echo "- 店铺ID: 具体店铺ID数字 或 'all'(查询所有有效店铺), 默认 37\n";
    echo "- 状态: awaiting_packaging|等待打包, awaiting_deliver|等待发货, delivering|配送中, delivered|已送达, all|所有状态 (默认: delivered)\n";
    echo "- 天数: 查询天数 (默认: 90)\n";
    echo "- debug: 开启调试模式 (可选)\n";
    echo "\n示例:\n";
    echo "php plod.php 37 delivered 90        # 查询37号店铺90天内已送达订单\n";
    echo "php plod.php all delivered 90       # 查询所有店铺90天内已送达订单\n";
    echo "php plod.php 37 all 7              # 查询37号店铺7天内所有订单\n";
    echo "php plod.php all awaiting_packaging 30 debug  # 查询所有店铺30天等待打包订单（调试模式）\n";
}