<?php
/**
 * 宽松模式订单状态更新脚本
 * 只根据posting_number匹配，不检查店铺ID
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

echo "=== 宽松模式订单状态更新工具 ===\n";
echo "注意: 此模式只根据订单号匹配，不验证店铺ID\n\n";

$filename = $argv[1] ?? '';
if (empty($filename)) {
    echo "使用方法: php loose_update_status.php [JSON文件名]\n";
    echo "示例: php loose_update_status.php 139_delivered_90days.json\n";
    exit(1);
}

$filepath = __DIR__ . '/data/' . $filename;

if (!file_exists($filepath)) {
    echo "错误: 文件不存在 - {$filename}\n";
    exit(1);
}

echo "处理文件: {$filename}\n";

$content = file_get_contents($filepath);
$orders = json_decode($content, true);

if ($orders === null) {
    echo "错误: JSON格式无效\n";
    exit(1);
}

if (empty($orders)) {
    echo "文件为空\n";
    exit(0);
}

echo "订单数量: " . count($orders) . "\n";
echo "开始更新...\n\n";

$stats = [
    'processed' => 0,
    'updated' => 0,
    'errors' => 0,
    'not_found' => 0,
    'no_change' => 0
];

$statusChanges = [];

foreach ($orders as $order) {
    if (!isset($order['posting_number']) || !isset($order['status'])) {
        $stats['errors']++;
        continue;
    }
    
    $postingNumber = $order['posting_number'];
    $newStatus = $order['status'];
    
    $stats['processed']++;
    
    try {
        // 只根据posting_number查询，不检查店铺ID
        $existingOrder = $DB->find('ozon_order', ['id', 'status', 'storeid'], ['posting_number' => $postingNumber]);
        
        if (!$existingOrder) {
            $stats['not_found']++;
            echo "  ✗ {$postingNumber}: 订单不存在\n";
            continue;
        }
        
        $oldStatus = $existingOrder['status'];
        
        // 检查状态是否需要更新
        if ($oldStatus === $newStatus) {
            $stats['no_change']++;
            continue;
        }
        
        // 更新状态
        $updateResult = $DB->update('ozon_order', 
            ['status' => $newStatus, 'updated_at' => date('Y-m-d H:i:s')], 
            ['posting_number' => $postingNumber]
        );
        
        if ($updateResult) {
            $stats['updated']++;
            
            // 记录状态变更统计
            $changeKey = $oldStatus . ' → ' . $newStatus;
            if (!isset($statusChanges[$changeKey])) {
                $statusChanges[$changeKey] = 0;
            }
            $statusChanges[$changeKey]++;
            
            echo "  ✓ {$postingNumber}: {$oldStatus} → {$newStatus} (店铺: {$existingOrder['storeid']})\n";
        } else {
            $stats['errors']++;
            echo "  ✗ {$postingNumber}: 更新失败\n";
        }
        
    } catch (Exception $e) {
        $stats['errors']++;
        echo "  ✗ {$postingNumber}: " . $e->getMessage() . "\n";
    }
}

echo "\n" . str_repeat('=', 50) . "\n";
echo "更新完成!\n\n";

echo "处理结果:\n";
echo "  总订单数: {$stats['processed']}\n";
echo "  更新成功: {$stats['updated']}\n";
echo "  无需更新: {$stats['no_change']}\n";
echo "  订单不存在: {$stats['not_found']}\n";
echo "  处理错误: {$stats['errors']}\n";

if (!empty($statusChanges)) {
    echo "\n状态变更统计:\n";
    arsort($statusChanges);
    foreach ($statusChanges as $change => $count) {
        echo "  {$change}: {$count} 个\n";
    }
}

echo "\n" . ($stats['updated'] > 0 ? "✅ 成功更新了 {$stats['updated']} 条订单状态!" : "ℹ️  没有订单状态需要更新") . "\n";