<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>利润统计明细</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../admin/view/lib/layui-v2.6.3/css/layui.css" media="all">
    <link rel="stylesheet" href="../admin/view/lib/font-awesome-4.7.0/css/font-awesome.min.css" media="all">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" media="all">
    <style>
        .layui-table-cell {
            height: auto !important;
            white-space: normal;
            padding: 8px !important;
            vertical-align: middle !important;
        }
        .layui-table td {
            height: auto !important;
            min-height: 80px;
        }
        .profit-positive {
            color: #5FB878;
            font-weight: bold;
        }
        .profit-negative {
            color: #FF5722;
            font-weight: bold;
        }
        /* 表格行高度自适应 */
        .layui-table tbody tr {
            height: auto !important;
        }
        /* 图片列样式 */
        .layui-table img {
            display: block;
            margin: 0 auto;
        }
        /* 订单号和商品名称列样式 */
        .layui-table .order-number,
        .layui-table .product-name {
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            line-height: 1.4;
        }
        :root {
            --primary: #2c7be5;
            --secondary: #6e84a3;
            --success: #00d97e;
            --warning: #f6c343;
            --danger: #e63757;
            --border: #edf2f9;
        }
        body {
            background: #f9fbfd;
            color: #2a3547;
        }
        .stats-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03);
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid var(--border);
            transition: transform 0.3s;
        }
        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.08);
        }
        .stats-item {
            text-align: center;
            border-right: 1px solid var(--border);
            padding: 0 15px;
        }
        .stats-item:last-child {
            border-right: none;
        }
        .stats-value {
            font-size: 28px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 8px;
        }
        .stats-label {
            color: var(--secondary);
            font-size: 14px;
            letter-spacing: 0.5px;
        }
        .search-form {
            background: #fff;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03);
            margin-bottom: 20px;
            border: 1px solid var(--border);
        }
        .layui-card {
            border-radius: 12px;
            box-shadow: 0 0.75rem 1.5rem rgba(18,38,63,.03);
            border: 1px solid var(--border);
        }
        .layui-btn {
            border-radius: 6px;
        }
        .page-title {
            color: var(--primary);
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .page-title i {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="layui-fluid">
        <!-- 页面标题 -->
        <div class="page-title">
            <i class="fas fa-chart-line"></i>
            利润统计明细
        </div>
        <!-- 统计卡片 -->
        <div class="stats-card">
            <div class="layui-row">
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="totalOrders">0</div>
                    <div class="stats-label">总订单数</div>
                </div>
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="totalSales">¥0</div>
                    <div class="stats-label">总销售额</div>
                </div>
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="totalCost">¥0</div>
                    <div class="stats-label">总采购成本</div>
                </div>
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="totalProfit">¥0</div>
                    <div class="stats-label">总利润</div>
                </div>
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="costProfitRate">0%</div>
                    <div class="stats-label">成本利润率</div>
                </div>
                <div class="layui-col-md2 stats-item">
                    <div class="stats-value" id="profitRate">0%</div>
                    <div class="stats-label">利润率</div>
                </div>
            </div>
        </div>

        <!-- 搜索表单 -->
        <div class="search-form">
            <form class="layui-form" lay-filter="searchForm">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <label class="layui-form-label">下单开始日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="start_date" id="start_date" placeholder="选择开始日期" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">下单结束日期</label>
                        <div class="layui-input-block">
                            <input type="text" name="end_date" id="end_date" placeholder="选择结束日期" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">订单状态</label>
                        <div class="layui-input-block">
                            <select name="status" id="status" multiple>
                                <option value="">全部状态</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">分组</label>
                        <div class="layui-input-block">
                            <select name="group_id" id="group_id">
                                <option value="">全部分组</option>
                            </select>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">店铺</label>
                        <div class="layui-input-block">
                            <select name="store_id" id="store_id">
                                <option value="">全部店铺</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                    <div class="layui-col-md3">
                        <label class="layui-form-label">发货开始时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="delivery_start_date" id="delivery_start_date" placeholder="选择发货开始时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">发货结束时间</label>
                        <div class="layui-input-block">
                            <input type="text" name="delivery_end_date" id="delivery_end_date" placeholder="选择发货结束时间" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">毛利率筛选</label>
                        <div class="layui-input-block">
                            <div class="layui-input-inline" style="width: 48%;">
                                <input type="number" name="profit_rate_min" id="profit_rate_min" placeholder="最小%" autocomplete="off" class="layui-input" step="0.01">
                            </div>
                            <div class="layui-form-mid">-</div>
                            <div class="layui-input-inline" style="width: 48%;">
                                <input type="number" name="profit_rate_max" id="profit_rate_max" placeholder="最大%" autocomplete="off" class="layui-input" step="0.01">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">退款筛选</label>
                        <div class="layui-input-block">
                            <select name="has_refund" id="has_refund">
                                <option value="">全部订单</option>
                                <option value="1">仅显示有退款订单</option>
                                <option value="0">仅显示无退款订单</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="layui-row layui-col-space15" style="margin-top: 15px;">
                    <div class="layui-col-md3">
                        <label class="layui-form-label">SKU筛选</label>
                        <div class="layui-input-block">
                            <input type="text" name="sku" id="sku" placeholder="输入SKU进行筛选" autocomplete="off" class="layui-input">
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">订单号筛选</label>
                        <div class="layui-input-block">
                            <textarea name="posting_numbers" id="posting_numbers" placeholder="输入订单号，一行一个" class="layui-textarea" style="height: 60px;"></textarea>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <label class="layui-form-label">运输中佣金率</label>
                        <div class="layui-input-block">
                            <input type="number" name="custom_commission_rate" id="custom_commission_rate" placeholder="自定义佣金率%" autocomplete="off" class="layui-input" step="0.01" min="0" max="100">
                            <div class="layui-word-aux">仅对运输中状态的订单生效</div>
                        </div>
                    </div>
                </div>
                <div class="layui-row" style="margin-top: 15px;">
                    <div class="layui-col-md12" style="text-align: center;">
                        <button type="button" class="layui-btn" lay-submit lay-filter="search">搜索</button>
                        <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                        <button type="button" class="layui-btn layui-btn-normal" id="exportBtn">导出数据</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table class="layui-hide" id="profitTable" lay-filter="profitTable"></table>
            </div>
        </div>
    </div>

    <script type="text/html" id="profitTpl">
        {{# if(d.calculated_profit >= 0) { }}
            <span class="profit-positive">¥{{parseFloat(d.calculated_profit || 0).toFixed(2)}}</span>
        {{# } else { }}
            <span class="profit-negative">¥{{parseFloat(d.calculated_profit || 0).toFixed(2)}}</span>
        {{# } }}
    </script>

    <script type="text/html" id="costProfitRateTpl">
        {{# var cost = parseFloat(d.cost || 0); }}
        {{# var profit = parseFloat(d.calculated_profit || 0); }}
        {{# if(cost > 0) { }}
            {{# var rate = ((profit / cost) * 100).toFixed(2); }}
            {{# if(parseFloat(rate) >= 0) { }}
                <span class="profit-positive">{{rate}}%</span>
            {{# } else { }}
                <span class="profit-negative">{{rate}}%</span>
            {{# } }}
        {{# } else { }}
            <span style="color: #999;">-</span>
        {{# } }}
    </script>

    <script type="text/html" id="statusTpl">
        {{# if(d.status === 'awaiting_verification') { }}
            <span class="layui-badge layui-bg-gray">未上传护照</span>
        {{# } else if(d.status === 'awaiting_packaging') { }}
            <span class="layui-badge layui-bg-blue">等待备货</span>
        {{# } else if(d.status === 'awaiting_deliver') { }}
            <span class="layui-badge layui-bg-orange">等待发货</span>
        {{# } else if(d.status === 'delivering') { }}
            <span class="layui-badge layui-bg-cyan">运输中</span>
        {{# } else if(d.status === 'delivered') { }}
            <span class="layui-badge layui-bg-green">已送达</span>
        {{# } else if(d.status === 'cancelled') { }}
            <span class="layui-badge layui-bg-red">已取消</span>
        {{# } else if(d.status === 'awaiting_registration') { }}
            <span class="layui-badge layui-bg-purple">移交给快递</span>
        {{# } else if(d.status === 'cancelled_from_split_pending') { }}
            <span class="layui-badge layui-bg-red">因货件拆分而取消</span>
        {{# } else { }}
            <span class="layui-badge layui-bg-gray">{{d.status || '未知'}}</span>
        {{# } }}
    </script>

    <script src="../admin/view/lib/layui-v2.6.3/layui.js" charset="utf-8"></script>
    <script>
        layui.use(['table', 'form', 'laydate', 'layer'], function(){
            var table = layui.table,
                form = layui.form,
                laydate = layui.laydate,
                layer = layui.layer,
                $ = layui.$;

            // 初始化日期选择器
            laydate.render({
                elem: '#start_date',
                type: 'date'
            });

            laydate.render({
                elem: '#end_date',
                type: 'date'
            });

            laydate.render({
                elem: '#delivery_start_date',
                type: 'datetime'
            });

            laydate.render({
                elem: '#delivery_end_date',
                type: 'datetime'
            });

            // 加载分组列表
            function loadGroups() {
                $.get('ajax_profit.php?act=get_groups', function(res) {
                    if (res.code === 0) {
                        var html = '<option value="">全部分组</option>';
                        res.data.forEach(function(group) {
                            html += '<option value="' + group.id + '">' + group.name + '</option>';
                        });
                        $('#group_id').html(html);
                        form.render('select');
                    }
                });
            }

            // 加载店铺列表
            function loadStores() {
                $.get('ajax_profit.php?act=get_stores', function(res) {
                    if (res.code === 0) {
                        var html = '<option value="">全部店铺</option>';
                        res.data.forEach(function(store) {
                            html += '<option value="' + store.id + '">' + store.storename + '</option>';
                        });
                        $('#store_id').html(html);
                        form.render('select');
                    }
                });
            }

            // 加载订单状态列表
            function loadStatuses() {
                $.get('ajax_profit.php?act=get_order_statuses', function(res) {
                    if (res.code === 0) {
                        var html = '<option value="">全部状态</option>';
                        var statusMap = {
                            'awaiting_verification': '未上传护照',
                            'awaiting_packaging': '等待备货',
                            'awaiting_deliver': '等待发货',
                            'delivering': '运输中',
                            'delivered': '已送达',
                            'cancelled': '已取消',
                            'awaiting_registration': '移交给快递',
                            'cancelled_from_split_pending': '因货件拆分而取消'
                        };
                        res.data.forEach(function(status) {
                            var displayName = statusMap[status.status] || status.status;
                            html += '<option value="' + status.status + '">' + displayName + '</option>';
                        });
                        $('#status').html(html);
                        form.render('select');
                    }
                });
            }

            // 更新统计数据
            function updateStats(stats) {
                $('#totalOrders').text(stats.total_orders);
                $('#totalSales').text('¥' + parseFloat(stats.total_sales || 0).toFixed(2));
                $('#totalCost').text('¥' + parseFloat(stats.total_cost || 0).toFixed(2));
                $('#totalProfit').text('¥' + parseFloat(stats.total_profit || 0).toFixed(2));
                
                var totalSales = parseFloat(stats.total_sales || 0);
                var totalProfit = parseFloat(stats.total_profit || 0);
                var totalCost = parseFloat(stats.total_cost || 0);
                
                // 计算利润率（基于销售额）
                var profitRate = totalSales > 0 ? ((totalProfit / totalSales) * 100).toFixed(2) : '0.00';
                $('#profitRate').text(profitRate + '%');
                
                // 计算成本利润率（基于成本）
                var costProfitRate = totalCost > 0 ? ((totalProfit / totalCost) * 100).toFixed(2) : '0.00';
                $('#costProfitRate').text(costProfitRate + '%');
            }

            // 获取搜索参数
            function getSearchParams() {
                var statusValues = $('#status').val();
                // 处理多选状态：如果选择了"全部状态"，则忽略其他选项
                if (statusValues && statusValues.includes('')) {
                    statusValues = '';
                } else if (statusValues && Array.isArray(statusValues)) {
                    statusValues = statusValues.join(',');
                }
                
                return {
                    start_date: $('#start_date').val(),
                    end_date: $('#end_date').val(),
                    delivery_start_date: $('#delivery_start_date').val(),
                    delivery_end_date: $('#delivery_end_date').val(),
                    status: statusValues || '',
                    group_id: $('#group_id').val(),
                    store_id: $('#store_id').val(),
                    sku: $('#sku').val(),
                    posting_numbers: $('#posting_numbers').val(),
                    profit_rate_min: $('#profit_rate_min').val(),
                    profit_rate_max: $('#profit_rate_max').val(),
                    has_refund: $('#has_refund').val(),
                    custom_commission_rate: $('#custom_commission_rate').val()
                };
            }

            // 初始化表格
            var tableIns = table.render({
                elem: '#profitTable',
                url: 'ajax_profit.php?act=get_profit_list',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                cols: [[
                    {field: 'primary_image', title: '商品图片', width: 100, templet: function(d) {
                        var imgSrc = d.primary_image || '../assets/img/syncing.png';
                        return '<img src="' + imgSrc + '" style="width: 60px; height: 60px; object-fit: contain; border: 1px solid #ddd; border-radius: 4px;">';
                    }},
                    {field: 'posting_number', title: '订单号', width: 200, templet: function(d) {
                        return '<div class="order-number">' + (d.posting_number || '') + '</div>';
                    }},
                    {field: 'order_name', title: '商品名称', width: 250, templet: function(d) {
                        return '<div class="product-name" title="' + (d.order_name || '') + '">' + (d.order_name || '') + '</div>';
                    }},
                    {field: 'sku', title: 'SKU', width: 120},
                    {field: 'storename', title: '店铺', width: 120},
                    {field: 'price', title: '总售价', width: 100, templet: function(d) {
                        var price = parseFloat(d.price || 0);
                        var quantity = parseFloat(d.quantity || 1);
                        var totalPrice = price * quantity;
                        return '¥' + totalPrice.toFixed(2);
                    }},
                    {field: 'cost', title: '采购成本', width: 100, templet: function(d) {
                        return '¥' + parseFloat(d.cost || 0).toFixed(2);
                    }},
                    {field: 't_income', title: '客户签收金额', width: 150, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_income_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_income || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 't_commission', title: '销售佣金', width: 120, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_commission_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_commission || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 't_agency_fee', title: '代理佣金', width: 120, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_agency_fee_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_agency_fee || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 't_delivery_fee', title: '物流费用', width: 120, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_delivery_fee_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_delivery_fee || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 't_acquiring_fee', title: '支付手续费', width: 130, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_acquiring_fee_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_acquiring_fee || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 't_return_amount', title: '退款金额', width: 120, sort: true, templet: function(d) { 
                        var cny = parseFloat(d.t_return_amount_cny || 0).toFixed(2);
                        var rub = parseFloat(d.t_return_amount || 0).toFixed(2);
                        var cnyClass = parseFloat(cny) < 0 ? 'style="color: red;"' : '';
                        var rubClass = parseFloat(rub) < 0 ? 'style="color: red;"' : '';
                        return '<span ' + cnyClass + '>¥' + cny + '</span><br><span class="text-muted" ' + rubClass + '>' + rub + ' ₽</span>'; 
                    }},
                    {field: 'calculated_profit', title: '计算利润', width: 100, templet: '#profitTpl'},
                    {field: 'cost_profit_rate', title: '成本利润率', width: 120, templet: '#costProfitRateTpl'},
                    {field: 'quantity', title: '数量', width: 80},
                    {field: 'status', title: '状态', width: 120, templet: '#statusTpl'},
                       {field: 'delivering_date', title: '发货时间', width: 160, sort: true, templet: function(d) {
                        return d.delivering_date ? d.delivering_date.substring(0, 16) : '-';
                    }},
                    {field: 'in_process_at', title: '下单时间', width: 160, sort: true, templet: function(d) {
                        return d.in_process_at ? d.in_process_at.substring(0, 16) : '-';
                    }}
                ]],
                done: function(res) {
                    if (res.stats) {
                        updateStats(res.stats);
                    }
                }
            });

            // 搜索功能
            form.on('submit(search)', function(data) {
                tableIns.reload({
                    where: getSearchParams(),
                    page: {
                        curr: 1
                    }
                });
                return false;
            });

            // 导出功能（使用 POST，避免超长 URL）
            $('#exportBtn').click(function() {
                var params = getSearchParams();
                layer.load(2);
                $.ajax({
                    url: 'ajax_profit.php?act=export_profit',
                    type: 'POST',
                    data: params,
                    dataType: 'json',
                    success: function(res) {
                        layer.closeAll('loading');
                        if (res.code === 0) {
                            layer.msg('导出成功，正在下载...', {icon: 1});
                            // 创建下载链接
                            var a = document.createElement('a');
                            a.href = res.data.download_url;
                            a.download = res.data.filename;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                        } else {
                            layer.msg(res.msg || '导出失败', {icon: 2});
                        }
                    },
                    error: function(jqXHR) {
                        layer.closeAll('loading');
                        var msg = '导出失败';
                        try {
                            var resp = JSON.parse(jqXHR.responseText || '{}');
                            if (resp && resp.msg) msg = resp.msg;
                        } catch (e) {}
                        layer.msg(msg, {icon: 2});
                    }
                });
            });

            // 初始化加载
            loadGroups();
            loadStores();
            loadStatuses();
        });
    </script>
</body>
</html>