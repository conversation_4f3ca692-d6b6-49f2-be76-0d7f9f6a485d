<?php
/**
 * 订单状态更新脚本
 * 从JSON文件读取订单数据并更新到ozon_order表的status字段
 */
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

class OrderStatusUpdater {
    private $DB;
    private $debug;
    
    public function __construct($debug = false) {
        global $DB;
        $this->DB = $DB;
        $this->debug = $debug;
    }
    
    /**
     * 显示使用帮助
     */
    public function showHelp() {
        echo "=== 订单状态更新工具 ===\n\n";
        echo "使用方法:\n";
        echo "php update_order_status.php [模式] [参数]\n\n";
        
        echo "模式1 - 更新单个JSON文件:\n";
        echo "  php update_order_status.php file [文件名] [debug]\n";
        echo "  示例: php update_order_status.php file 37_delivered_90days.json\n\n";
        
        echo "模式2 - 更新指定店铺的所有JSON文件:\n";
        echo "  php update_order_status.php store [店铺ID] [debug]\n";
        echo "  示例: php update_order_status.php store 37\n\n";
        
        echo "模式3 - 更新所有JSON文件:\n";
        echo "  php update_order_status.php all [debug]\n";
        echo "  示例: php update_order_status.php all\n\n";
        
        echo "模式4 - 更新指定状态的文件:\n";
        echo "  php update_order_status.php status [状态名] [debug]\n";
        echo "  示例: php update_order_status.php status delivered\n\n";
        
        echo "模式5 - 数据库诊断:\n";
        echo "  php update_order_status.php diagnose [JSON文件名]\n";
        echo "  示例: php update_order_status.php diagnose 139_delivered_90days.json\n\n";
        
        echo "参数说明:\n";
        echo "  debug: 添加此参数开启调试模式\n\n";
    }
    
    /**
     * 获取data目录下的所有JSON文件
     */
    public function getJsonFiles($pattern = '*.json') {
        $dataDir = __DIR__ . '/data';
        if (!is_dir($dataDir)) {
            echo "错误: data目录不存在\n";
            return [];
        }
        
        $files = glob($dataDir . '/' . $pattern);
        return array_map('basename', $files);
    }
    
    /**
     * 读取并解析JSON文件
     */
    public function readJsonFile($filename) {
        $filepath = __DIR__ . '/data/' . $filename;
        
        if (!file_exists($filepath)) {
            echo "错误: 文件不存在 - {$filename}\n";
            return false;
        }
        
        $content = file_get_contents($filepath);
        if ($content === false) {
            echo "错误: 无法读取文件 - {$filename}\n";
            return false;
        }
        
        $data = json_decode($content, true);
        if ($data === null) {
            echo "错误: JSON格式无效 - {$filename}\n";
            return false;
        }
        
        if ($this->debug) {
            echo "成功读取文件 {$filename}，包含 " . count($data) . " 条订单\n";
        }
        
        return $data;
    }
    
    /**
     * 更新单条订单状态
     */
    public function updateOrderStatus($postingNumber, $status, $storeId = null) {
        try {
            // 构建查询条件 - 注意：数据库表中店铺字段是 storeid，不是 store_id
            $conditions = ['posting_number' => $postingNumber];
            if ($storeId !== null) {
                $conditions['storeid'] = $storeId;  // 使用 storeid 而不是 store_id
            }
            
            // 先尝试只用订单号查询，看订单是否存在
            $orderExists = $this->DB->find('order', ['posting_number', 'status', 'storeid', 'uid'], 
                ['posting_number' => $postingNumber]);
            
            if (!$orderExists) {
                if ($this->debug) {
                    echo "  订单完全不存在: {$postingNumber}\n";
                    // 尝试模糊查询
                    $similarOrders = $this->DB->getAll("SELECT posting_number, status, storeid FROM order WHERE posting_number LIKE ? LIMIT 3", 
                        ['%' . substr($postingNumber, -6) . '%']);
                    if ($similarOrders) {
                        echo "  相似订单号: ";
                        foreach ($similarOrders as $similar) {
                            echo "{$similar['posting_number']} ";
                        }
                        echo "\n";
                    }
                }
                return ['status' => 'not_found', 'message' => '订单不存在'];
            }
            
            // 如果指定了店铺ID，检查是否匹配
            if ($storeId !== null && $orderExists['storeid'] != $storeId) {
                if ($this->debug) {
                    echo "  店铺ID不匹配: {$postingNumber} (JSON:{$storeId} vs DB:{$orderExists['storeid']})\n";
                }
                // 不强制要求店铺ID匹配，继续处理
            }
            
            // 检查状态是否需要更新
            if ($orderExists['status'] === $status) {
                if ($this->debug) {
                    echo "  状态无需更新: {$postingNumber} ({$status})\n";
                }
                return ['status' => 'no_change', 'message' => '状态无需更新'];
            }
            
            // 更新状态 - 只用订单号作为条件
            $updateResult = $this->DB->update('order', 
                ['status' => $status], 
                ['posting_number' => $postingNumber]
            );
            
            if ($updateResult) {
                if ($this->debug) {
                    echo "  ✓ 更新成功: {$postingNumber} {$orderExists['status']} → {$status} (店铺:{$orderExists['storeid']})\n";
                }
                return [
                    'status' => 'updated', 
                    'message' => '更新成功',
                    'old_status' => $orderExists['status'],
                    'new_status' => $status
                ];
            } else {
                echo "  ✗ 更新失败: {$postingNumber}\n";
                return ['status' => 'error', 'message' => '数据库更新失败'];
            }
            
        } catch (Exception $e) {
            echo "  ✗ 异常错误: {$postingNumber} - " . $e->getMessage() . "\n";
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 批量更新订单状态
     */
    public function batchUpdateFromJson($jsonData, $filename = '') {
        echo "处理文件: {$filename}\n";
        
        if (empty($jsonData)) {
            echo "  文件为空，跳过\n\n";
            return ['processed' => 0, 'updated' => 0, 'errors' => 0, 'not_found' => 0, 'no_change' => 0];
        }
        
        $stats = [
            'processed' => 0,
            'updated' => 0,
            'errors' => 0,
            'not_found' => 0,
            'no_change' => 0
        ];
        
        $statusChanges = [];
        
        foreach ($jsonData as $order) {
            if (!isset($order['posting_number']) || !isset($order['status'])) {
                echo "  ✗ 数据格式错误，跳过\n";
                $stats['errors']++;
                continue;
            }
            
            $postingNumber = $order['posting_number'];
            $status = $order['status'];
            $storeId = $order['store_id'] ?? null;
            
            $result = $this->updateOrderStatus($postingNumber, $status, $storeId);
            
            $stats['processed']++;
            
            switch ($result['status']) {
                case 'updated':
                    $stats['updated']++;
                    $oldStatus = $result['old_status'];
                    $newStatus = $result['new_status'];
                    
                    if (!isset($statusChanges[$oldStatus . ' → ' . $newStatus])) {
                        $statusChanges[$oldStatus . ' → ' . $newStatus] = 0;
                    }
                    $statusChanges[$oldStatus . ' → ' . $newStatus]++;
                    break;
                    
                case 'not_found':
                    $stats['not_found']++;
                    break;
                    
                case 'no_change':
                    $stats['no_change']++;
                    break;
                    
                case 'error':
                default:
                    $stats['errors']++;
                    break;
            }
        }
        
        // 显示统计信息
        echo "  处理完成:\n";
        echo "    总订单数: {$stats['processed']}\n";
        echo "    更新成功: {$stats['updated']}\n";
        echo "    无需更新: {$stats['no_change']}\n";
        echo "    订单不存在: {$stats['not_found']}\n";
        echo "    错误: {$stats['errors']}\n";
        
        if (!empty($statusChanges)) {
            echo "  状态变更统计:\n";
            foreach ($statusChanges as $change => $count) {
                echo "    {$change}: {$count} 个\n";
            }
        }
        
        echo "\n";
        return $stats;
    }
    
    /**
     * 更新单个文件
     */
    public function updateFromFile($filename) {
        echo "=== 更新单个文件: {$filename} ===\n";
        
        $jsonData = $this->readJsonFile($filename);
        if ($jsonData === false) {
            return false;
        }
        
        $stats = $this->batchUpdateFromJson($jsonData, $filename);
        
        echo "文件 {$filename} 处理完成!\n";
        echo "更新了 {$stats['updated']} 条订单状态\n\n";
        
        return $stats;
    }
    
    /**
     * 更新指定店铺的所有文件
     */
    public function updateFromStore($storeId) {
        echo "=== 更新店铺 {$storeId} 的所有文件 ===\n";
        
        $files = $this->getJsonFiles($storeId . '_*.json');
        
        if (empty($files)) {
            echo "没有找到店铺 {$storeId} 的JSON文件\n";
            return false;
        }
        
        echo "找到 " . count($files) . " 个文件\n\n";
        
        $totalStats = [
            'processed' => 0,
            'updated' => 0,
            'errors' => 0,
            'not_found' => 0,
            'no_change' => 0
        ];
        
        foreach ($files as $filename) {
            $jsonData = $this->readJsonFile($filename);
            if ($jsonData !== false) {
                $stats = $this->batchUpdateFromJson($jsonData, $filename);
                
                foreach ($totalStats as $key => $value) {
                    $totalStats[$key] += $stats[$key];
                }
            }
        }
        
        echo "店铺 {$storeId} 所有文件处理完成!\n";
        echo "总计更新了 {$totalStats['updated']} 条订单状态\n\n";
        
        return $totalStats;
    }
    
    /**
     * 更新所有JSON文件
     */
    public function updateAll() {
        echo "=== 更新所有JSON文件 ===\n";
        
        $files = $this->getJsonFiles();
        
        if (empty($files)) {
            echo "data目录下没有找到JSON文件\n";
            return false;
        }
        
        // 过滤掉合并文件（all_stores_开头的文件）
        $files = array_filter($files, function($filename) {
            return !str_starts_with($filename, 'all_stores_');
        });
        
        echo "找到 " . count($files) . " 个文件\n\n";
        
        $totalStats = [
            'processed' => 0,
            'updated' => 0,
            'errors' => 0,
            'not_found' => 0,
            'no_change' => 0
        ];
        
        foreach ($files as $filename) {
            $jsonData = $this->readJsonFile($filename);
            if ($jsonData !== false) {
                $stats = $this->batchUpdateFromJson($jsonData, $filename);
                
                foreach ($totalStats as $key => $value) {
                    $totalStats[$key] += $stats[$key];
                }
            }
        }
        
        echo "所有文件处理完成!\n";
        echo "总计更新了 {$totalStats['updated']} 条订单状态\n\n";
        
        return $totalStats;
    }
    
    /**
     * 更新指定状态的文件
     */
    public function updateByStatus($status) {
        echo "=== 更新状态为 {$status} 的文件 ===\n";
        
        $files = $this->getJsonFiles('*_' . $status . '_*.json');
        
        if (empty($files)) {
            echo "没有找到状态为 {$status} 的JSON文件\n";
            return false;
        }
        
        echo "找到 " . count($files) . " 个文件\n\n";
        
        $totalStats = [
            'processed' => 0,
            'updated' => 0,
            'errors' => 0,
            'not_found' => 0,
            'no_change' => 0
        ];
        
        foreach ($files as $filename) {
            $jsonData = $this->readJsonFile($filename);
            if ($jsonData !== false) {
                $stats = $this->batchUpdateFromJson($jsonData, $filename);
                
                foreach ($totalStats as $key => $value) {
                    $totalStats[$key] += $stats[$key];
                }
            }
        }
        
        echo "状态 {$status} 的所有文件处理完成!\n";
        echo "总计更新了 {$totalStats['updated']} 条订单状态\n\n";
        
        return $totalStats;
    }
}

// 主程序
if ($argc < 2) {
    $updater = new OrderStatusUpdater();
    $updater->showHelp();
    exit(1);
}

$mode = $argv[1];
$debug = in_array('debug', $argv);
$updater = new OrderStatusUpdater($debug);

switch ($mode) {
    case 'help':
    case '--help':
    case '-h':
        $updater->showHelp();
        break;
        
    case 'file':
        $filename = $argv[2] ?? '';
        if (empty($filename)) {
            echo "错误: 请提供文件名\n";
            $updater->showHelp();
            exit(1);
        }
        
        $result = $updater->updateFromFile($filename);
        exit($result ? 0 : 1);
        
    case 'store':
        $storeId = $argv[2] ?? '';
        if (empty($storeId)) {
            echo "错误: 请提供店铺ID\n";
            $updater->showHelp();
            exit(1);
        }
        
        $result = $updater->updateFromStore($storeId);
        exit($result ? 0 : 1);
        
    case 'all':
        $result = $updater->updateAll();
        exit($result ? 0 : 1);
        
    case 'status':
        $status = $argv[2] ?? '';
        if (empty($status)) {
            echo "错误: 请提供状态名\n";
            $updater->showHelp();
            exit(1);
        }
        
        $result = $updater->updateByStatus($status);
        exit($result ? 0 : 1);
        
    case 'list':
        echo "=== data目录下的JSON文件 ===\n";
        $files = $updater->getJsonFiles();
        if (empty($files)) {
            echo "没有找到JSON文件\n";
        } else {
            foreach ($files as $filename) {
                echo "- {$filename}\n";
            }
        }
        break;
        
    default:
        echo "错误: 未知的模式 '{$mode}'\n\n";
        $updater->showHelp();
        exit(1);
}