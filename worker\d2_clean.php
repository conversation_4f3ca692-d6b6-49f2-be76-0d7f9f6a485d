<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/../includes/common.php';

// 直接API调用辅助函数（用于调试）
function directApiCall($storeRow, $filter, $limit, $offset) {
    $url = 'https://api-seller.ozon.ru/v3/posting/fbs/list';
    $payload = [
        'dir' => 'ASC',
        'filter' => $filter,
        'limit' => $limit,
        'offset' => $offset,
        'with' => ['financial_data' => true]
    ];
    
    $ch = curl_init();
    $headers = [
        'Client-Id: ' . $storeRow['ClientId'],
        'Api-Key: ' . $storeRow['key'],
        'Content-Type: application/json'
    ];
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_POSTFIELDS => json_encode($payload, JSON_UNESCAPED_UNICODE),
        CURLOPT_TIMEOUT => 30,
        CURLOPT_CONNECTTIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    
    $result = [
        'http_code' => $httpCode,
        'curl_error' => $curlError,
        'payload_sent' => $payload
    ];
    
    if ($response) {
        $decoded = json_decode($response, true);
        if ($decoded) {
            $result['response'] = $decoded;
            if (isset($decoded['result']['postings'])) {
                $result['postings_count'] = count($decoded['result']['postings']);
            }
        } else {
            $result['raw_response'] = $response;
        }
    }
    
    return $result;
}

// 从命令行参数获取配置，或使用默认值
$storeId = $argv[1] ?? 37;
$status = $argv[2] ?? 'delivered';  // awaiting_packaging, awaiting_deliver, delivering, delivered, all
$days = $argv[3] ?? 90;  // 查询天数

// 获取店铺数据
$row = $DB->find('store', '*', ['id' => $storeId]);
if (!$row) {
    echo "错误: 找不到ID为{$storeId}的店铺\n";
    
    // 显示可用店铺
    echo "\n可用店铺列表:\n";
    $stores = $DB->findAll('store', ['id', 'ClientId', 'status'], []);
    foreach ($stores as $store) {
        echo "- ID: {$store['id']}, ClientId: {$store['ClientId']}, 状态: " . ($store['status'] ?? '未知') . "\n";
    }
    exit(1);
}

if ($row) {
    $client = new \lib\OzonApiClient($row['ClientId'], $row['key']);
    
    $offset = 0;
    $orderNumbers = [];
    $totalOrders = 0;
    
    // 计算时间范围（根据时间计算规范使用准确格式，结束时间设为昨天）
    $since = new DateTime("-{$days} days", new DateTimeZone('UTC'));
    $to = new DateTime('yesterday', new DateTimeZone('UTC'));
    $to->setTime(23, 59, 59); // 设置为昨天的23:59:59
    
    // 根据状态设置过滤器
    if ($status === 'all') {
        $filter = [
            'since' => $since->format('Y-m-d\TH:i:s.v\Z'),
            'to' => $to->format('Y-m-d\TH:i:s.v\Z')
        ];
    } else {
        $filter = [
            'status' => $status,
            'since' => $since->format('Y-m-d\TH:i:s.v\Z'),
            'to' => $to->format('Y-m-d\TH:i:s.v\Z')
        ];
    }
    
    // 添加分页计数器
    $pageCount = 0;
    $maxPages = 100; // 防止无限循环的最大页数限制
    
    echo "开始分页获取数据...\n";
    echo "时间范围: " . $since->format('Y-m-d H:i:s') . " 到 " . $to->format('Y-m-d H:i:s') . "\n";
    echo "查询条件: " . json_encode($filter, JSON_UNESCAPED_UNICODE) . "\n\n";
    
    while (true) {
        $pageCount++;
        
        try {
            // 使用OzonApiClient的getFbsPostingListV3方法
            echo "=== 第 {$pageCount} 页 ===\n";
            echo "当前offset: {$offset}\n";
            
            // 为了调试，我们手动构建请求参数
            $debugPayload = [
                'dir' => 'ASC',
                'filter' => $filter,
                'limit' => 100,
                'offset' => $offset,
                'with' => ['financial_data' => true]
            ];
            echo "原始请求参数: " . json_encode($debugPayload, JSON_UNESCAPED_UNICODE) . "\n";
            
            echo "正在调用 getFbsPostingListV3(limit=100, offset={$offset})...\n";
            
            // 为了调试，我们也尝试直接调用API
            if ($pageCount <= 2) {
                echo "\n--- 直接API调用调试 ---\n";
                $directResult = directApiCall($row, $filter, 100, $offset);
                echo "直接API结果: " . json_encode($directResult, JSON_UNESCAPED_UNICODE) . "\n";
                echo "--- 直接API调用结束 ---\n\n";
            }
            
            $data = $client->getFbsPostingListV3($filter, 100, $offset);
            
            // 显示API返回的结构
            echo "API响应结构: " . (isset($data['result']) ? '有result字段' : '无result字段') . "\n";
            if (isset($data['result'])) {
                echo "postings字段: " . (isset($data['result']['postings']) ? '存在' : '不存在') . "\n";
                if (isset($data['result']['postings'])) {
                    $returnedCount = count($data['result']['postings']);
                    echo "本页返回数据量: {$returnedCount} 条\n";
                } else {
                    echo "postings字段为空\n";
                }
            }
            
            // 检查错误
            if (isset($data['error'])) {
                echo "API返回错误: " . json_encode($data['error'], JSON_UNESCAPED_UNICODE) . "\n";
                break;
            }
            
            if (isset($data['result']['postings']) && !empty($data['result']['postings'])) {
                $currentBatchCount = count($data['result']['postings']);
                echo "处理 {$currentBatchCount} 条订单数据\n";
                
                foreach ($data['result']['postings'] as $item) {
                    $orderNumbers[] = [
                        'posting_number' => $item['posting_number'],
                        'status' => $item['status'] ?? '未知',
                        'created_at' => $item['created_at'] ?? '',
                        'in_process_at' => $item['in_process_at'] ?? ''
                    ];
                    $totalOrders++;
                }
                
                $offset += $currentBatchCount;
                echo "累计订单数: {$totalOrders}\n";
                echo "新的offset: {$offset}\n";
                
                // 如果返回的数据量小于limit，说明没有更多数据了
                if ($currentBatchCount < 100) {
                    echo "返回数据量({$currentBatchCount}) < 100，已获取所有数据\n";
                    break;
                }
                
                echo "返回数据量为100条，继续获取下一页...\n";
                echo "等待0.5秒...\n\n";
                
                // 添加延迟避免API限流
                usleep(500000); // 0.5秒延迟
            } else {
                echo "本页没有获取到订单数据\n";
                if (!isset($data['result']['postings'])) {
                    echo "原因: postings字段不存在\n";
                } elseif (empty($data['result']['postings'])) {
                    echo "原因: postings字段为空数组\n";
                }
                echo "结束分页循环\n";
                break;
            }
        } catch (Exception $e) {
            echo "第 {$pageCount} 页API调用异常: " . $e->getMessage() . "\n";
            break;
        }
        
        // 防止无限循环，设置最大偏移量和页数限制
        if ($offset >= 10000) {
            echo "已达到最大偏移量限制(10000)\n";
            break;
        }
        
        if ($pageCount >= $maxPages) {
            echo "已达到最大页数限制({$maxPages})\n";
            break;
        }
    }
    
    echo "\n分页获取完成！\n";
    echo "总页数: {$pageCount}\n";
    echo "总订单数: {$totalOrders}\n\n";
    
    // 保存订单号到JSON文件，文件名包含状态和天数信息
    $filename = "{$storeId}_{$status}_{$days}days.json";
    $filePath = __DIR__ . '/data/' . $filename;
    
    // 确保目录存在
    if (!is_dir(dirname($filePath))) {
        mkdir(dirname($filePath), 0755, true);
    }
    
    // 写入JSON文件
    file_put_contents($filePath, json_encode($orderNumbers, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
    
    // 显示状态统计
    if (!empty($orderNumbers)) {
        $statusCount = [];
        foreach ($orderNumbers as $order) {
            $statusCount[$order['status']] = ($statusCount[$order['status']] ?? 0) + 1;
        }
    }
    
    $result = [
        'code' => 0,
        'message' => '订单数据已保存',
        'filename' => $filename,
        'order_count' => $totalOrders,
        'file_path' => $filePath,
        'status_filter' => $status,
        'days' => $days,
        'time_range' => [
            'since' => $since->format('Y-m-d H:i:s'),
            'to' => $to->format('Y-m-d H:i:s')
        ]
    ];
} else {
    $result = [
        'code' => -1,
        'msg' => '店铺数据不存在'
    ];
}

echo json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";